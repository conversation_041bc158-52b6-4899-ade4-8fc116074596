image: node:20

pipelines:
  branches:
    development:
      - step:
          name: Install & Build
          caches:
            - node
            - npm
          script:
            - npm install
            # - npm run test
            - DISABLE_ESLINT_PLUGIN=true npm run build:staging
          artifacts:
            - build/**
      - step:
          name: Deploy to S3
          script:
            - pipe: atlassian/aws-s3-deploy:1.1.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: $S3_BUCKET
                LOCAL_PATH: 'build'
                ACL: 'public-read'
      - step:
          name: Invalidate Cloudfront Cache
          # caches:
            # - aws-cli
          script:
            - apt-get update && apt-get install -y unzip;
            - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip";
            - unzip awscliv2.zip; ./aws/install --update;
            - aws cloudfront create-invalidation --distribution-id "$CLOUDFRONT_ID" --paths "/*";

definitions:
  caches:
    npm: ~/.npm
    aws-cli: /usr/local/aws-cli/v2
