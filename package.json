{"name": "millenium", "version": "1.5.5", "description": "Millenium Strategies Admin Panel Client", "private": true, "homepage": "", "scripts": {"start": "node --max-old-space-size=4096 scripts/start.js", "build": "node scripts/build.js", "test": "node scripts/test.js", "start:development": "env-cmd -f .env.development npm run start", "dev": "env-cmd -f .env.development node --max-old-space-size=8000 scripts/start.js", "build:staging": "env-cmd -f .env.staging npm run build", "build:mock": "DISABLE_ESLINT_PLUGIN=true env-cmd -f .env.mock npm run build", "build:production": "DISABLE_ESLINT_PLUGIN=true env-cmd -f .env.production npm run build", "prepare": "husky install", "format": "prettier --write .", "stylelint": "stylelint 'src/assets/**/*.scss' --fix"}, "lint-staged": {"**/*": "eslint --fix"}, "eslintConfig": {"extends": "react-app", "plugins": ["jsx-a11y"], "rules": {"no-whitespace-before-property": "off"}, "overrides": [{"files": ["*-index.js"], "rules": {"no-unused-expressions": "off"}}], "reportUnusedDisableDirectives": true, "noInlineConfig": true}, "dependencies": {"@babel/core": "^7.23.3", "@babel/runtime": "^7.22.5", "@datadog/browser-logs": "^5.21.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fortawesome/fontawesome-svg-core": "^1.2.35", "@fortawesome/free-solid-svg-icons": "^5.15.3", "@fortawesome/react-fontawesome": "^0.1.14", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/x-charts": "^7.3.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "angular-expressions": "^1.1.3", "autosuggest-highlight": "^3.3.4", "axios": "^1.6.5", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "bootstrap": "^5.3.2", "bootstrap-scss": "^4.5.0", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "chart.js": "^3.8.0", "classnames": "^2.2.6", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "date-fns": "^2.0.0-alpha.18", "dayjs": "^1.11.11", "docxtemplater": "^3.21.1", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "env-cmd": "^10.1.0", "eslint-config-react-app": "^7.0.0", "eslint-webpack-plugin": "^3.1.1", "feather-icons": "^4.28.0", "file-loader": "^6.2.0", "file-saver": "^2.0.5", "formik": "^2.2.9", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "immutability-helper": "^3.1.1", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "knob": "^1.1.0", "lodash": "^4.17.21", "lodash.assign": "^4.2.0", "lodash.isnan": "^3.0.2", "lodash.isnil": "^4.0.0", "lodash.mean": "^4.1.0", "lodash.meanby": "^4.10.0", "lodash.sum": "^4.0.2", "lodash.sumby": "^4.6.0", "lodash.without": "^4.4.0", "lzwcompress": "^1.1.0", "mini-css-extract-plugin": "^2.4.5", "nanoid": "^3.1.22", "path-browserify": "^1.0.1", "pdfjs-dist": "^4.8.69", "pizzip": "^3.0.6", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react-app-polyfill": "^3.0.0", "react-bootstrap": "^1.6.4", "react-bootstrap-table-next": "^4.0.3", "react-bootstrap-table2-overlay": "^2.0.0", "react-bootstrap-table2-paginator": "^2.1.2", "react-bootstrap-typeahead": "^5.1.4", "react-chartjs-2": "^4.1.0", "react-copy-to-clipboard": "^5.0.4", "react-currency-input-field": "^3.8.0", "react-datepicker": "^4.24.0", "react-dev-utils": "^12.0.1", "react-dropzone": "^14.2.3", "react-dropzone-uploader": "^2.11.0", "react-feather": "^2.0.9", "react-helmet": "^6.1.0", "react-icons": "^4.3.1", "react-pdf": "^9.2.1", "react-popper": "^2.2.5", "react-refresh": "^0.11.0", "react-router": "^6.2.1", "react-router-dom": "^6.23.0", "react-scrollbar": "^0.5.6", "react-sortablejs": "^6.0.0", "react-toastify": "^7.0.3", "react-transition-group": "^4.4.1", "reactstrap": "^8.9.0", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass": "^1.56.1", "sass-loader": "^12.3.0", "semver": "^7.3.5", "sortablejs": "^1.13.0", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "styled-components": "^5.2.3", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "util": "^0.12.5", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1", "yup": "^0.32.11"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.22.5", "@types/autosuggest-highlight": "^3.2.3", "@types/file-saver": "^2.0.7", "@types/lodash.isnan": "^3.0.9", "@types/lodash.isnil": "^4.0.9", "@types/lodash.mean": "^4.1.9", "@types/lodash.sum": "^4.0.9", "@types/lodash.sumby": "^4.6.9", "@types/lodash.without": "^4.4.9", "@types/react-bootstrap-table-next": "^4.0.26", "@types/react-bootstrap-table2-paginator": "^2.1.5", "@types/react-bootstrap-typeahead": "^5.1.12", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-datepicker": "^4.19.3", "@types/react-dom": "^18.2.17", "@types/react-helmet": "^6.1.9", "@types/sortablejs": "^1.10.6", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "babel-plugin-uglify": "^1.0.2", "eslint": "^8.54.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "husky": "^8.0.3", "lint-staged": "^15.1.0", "moment": "^2.26.0", "moment-timezone": "^0.5.31", "prettier": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "stylelint": "^16.4.0", "stylelint-config-standard-scss": "^13.1.0", "thread-loader": "^4.0.2", "typescript": "^5.3.2"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": [], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}