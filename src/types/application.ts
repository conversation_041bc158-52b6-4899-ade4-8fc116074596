import { Program } from 'types/program';
import { CustomField } from 'shared/CustomFields';

export interface ActionLogRecord {
  application: {
    newState: Record<string, string>;
    oldState: Record<string, string>;
  };
  changedColumns: string[];
  date: string[];
  status: string;
  user: {
    userId?: number;
    userName?: string;
  };
}

export interface Application {
  id?: number;
  name?: string;
  programId?: number;
  assigneeId?: number;
  assignee?: {
    id: number;
    name: string;
  };
  clientId?: number;
  client?: {
    id: number;
    name: string;
    state: string;
    awardsEnabled: boolean;
    privateAwardsManagement: boolean;
  };
  actionLogs?: {
    appId: number;
    state: ActionLogRecord[] | string;
  };
  programData?: Program;
  assigneeName?: string;
  clientName?: string;
  funder?: string;
  fundingAmount?: number;
  varyingFundingAmount?: string;
  amountVaries?: boolean;
  status?: number | string;
  source?: number;
  category?: number;
  grantPurpose?: string;
  departmentNotified?: string;
  matchRequirements?: string;
  performancePeriod?: string;
  submissionStatus?: string;
  estimatedResponse?: string;
  customFields?: Record<string, string | number> | string[] | string;
  summaryFile?: string;
  startsAt?: Date | string | null;
  endsAt?: Date | string | null;
  awardDate?: Date | string | null;
  notifyDate?: Date | string | null;
  createdAt?: Date | string | null;
  updatedAt?: Date | string | null;
  dateNotified?: Date | string | null;
  dateAwarded?: Date | string | null;
  submissionDate?: Date | string | null;
  awardDoesNotExist?: boolean; // Psuedo field
}

export interface NewApplication {
  programId: number;
  assigneeId: number;
  clientId: number;
  name: string;
  funder: string;
  fundingAmount: number;
  varyingFundingAmount: string;
  amountVaries: boolean;
  source: number;
  startsAt: Date | string | null;
  endsAt: Date | string | null;
  matchRequirements: string;
  performancePeriod: string;
  summaryFile: string;
  estimatedResponse: string;
  customFields: string | string[] | CustomField[] | Record<string, string | number>;
  category: number;
  departmentNotified: string;
  submissionStatus: string;
  status: number | string;
  emails: string[];
  message: string;
  dateNotified: Date | string | null;
  dateAwarded: Date | string | null;
  submissionDate: Date | string | null;
  grantPurpose: string;
  generateAward: boolean; // Psuedo field
}
