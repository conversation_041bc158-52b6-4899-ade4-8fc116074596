import { User } from 'types/user';

export interface Client {
  id: number;
  primaryUserId?: number;
  clientDirectorId?: number;
  clientType?: string;
  name: string;
  population: string;
  state: string;
  taxId?: string;
  dunsNumber?: string;
  memoTo?: string;
  memoCc?: string;
  contractStartedAt: string | null;
  contractEndsAt: string | null;
  customFields: string;
  awardsEnabled: boolean;
  privateAwardsManagement: boolean;
  enabled: true;
  createdAt: string;
  updatedAt: string;
  applicationUsers?: User[];
  usersCreatedByClient?: User[];
  awardUsers?: User[];
  canCreateAward: boolean;
  states?: string[];
  counties?: string[];
  billingType?: string;
}

export type CreateClientPayload = Partial<{
  type: string;
  name: string;
  population: string;
  state: string;
  taxId: string;
  duns: string;
  contractDate: string | Date | null;
  contractEndsAt: string | Date | null;
  memoTo: string;
  memoCc: string;
  customFields: string;
  assignedUsers: number[];
  awardUsers: number[];
  primaryUser: number;
  clientDirectorId: number;
  awardsEnabled?: boolean;
  canCreateAward?: boolean;
  privateAwardsManagement?: boolean;
  states?: string[];
  counties?: string[];
  billingType?: string;
  clientType: string | number;
}>;

export interface UpdateClientPayload extends CreateClientPayload {
  id: number;
}

export interface ClientContact {
  id: number;
  clientId?: number;
  name: string;
  title: string;
  email: string;
  phoneNumber: string;
}

export interface CreateClientContactPayload {
  clientId: number;
  name: string;
  title: string;
  email: string;
  phone: string;
}

export interface ClientCredentials {
  clientId: number;
  portalName: string;
  username: string;
  password: string;
}

export interface ClientFile {
  id: number;
  clientId: number;
  name: string;
  fileUrl: string;
  type: string;
  size: string;
  createdAt: string;
}

export interface ClientsList {
  rows: Client[];
  pages: number;
  entries: number;
}

export interface GetClientMemoPayload {
  clientId: number;
  employeeId: string | number;
  contractStartedAt: string | null;
  includeDepartmentNotified: boolean;
  includeMatchRequirements: boolean;
  includePerformancePeriod: boolean;
}
