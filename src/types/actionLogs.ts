import { Award } from './awards';

export interface AwardStateChange {
  user: { userName: string };
  award: {
    oldState: Partial<Award> & Partial<{ [key: string]: string | boolean }>;
    newState: Partial<Award> & Partial<{ [key: string]: string | boolean }>;
  };
  status: string;
  date: string;
  changedColumns: (keyof Award)[];
}

export interface ActionLogs {
  state: AwardStateChange[];
}
