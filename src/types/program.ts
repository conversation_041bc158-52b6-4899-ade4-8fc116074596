export interface Program {
  id: number;
  name: string;
  funder?: string;
  fundingAmount?: number;
  amountVaries?: boolean;
  varyingFundingAmount?: string;
  source?: number | number[];
  category?: number | number[];
  states?: string | string[];
  counties?: string | string[];
  matchRequirements?: string;
  performancePeriod?: string;
  estimatedResponse?: string;
  customFields?: string;
  departmentNotified?: boolean;
  summaryData?: string;
  summaryFile?: string;
  orgTypes?: string[] | string;
  showForFlexClient?: boolean;
  startsAt?: string;
  endsAt?: Date | string | null;
  createdAt?: Date;
  updatedAt?: Date;
}
