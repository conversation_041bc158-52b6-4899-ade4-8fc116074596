export interface FilterData {
  clients: number[];
  funders: { name: string }[];
  programNames: { name: string }[];
  users?: { id: number; name: string }[];
  status: string;
  startDate: string;
  endDate: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export interface Filter {
  id: number;
  name: string;
  filterData: FilterData;
}

export interface SavedFilter {
  id: number;
  moduleId: number;
  userId: number;
  title: string;
  filterData: FilterData;
}
