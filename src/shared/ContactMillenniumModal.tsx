import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Row, Col } from 'react-bootstrap';
import { useState } from 'react';
import { Stack } from '@mui/material';
import useUserSession from 'hooks/useUserSession';
import api from 'services/apiService';
import { toast } from 'react-toastify';

interface ContactMillenniumModalProps {
  show: boolean;
  onHide: () => void;
}

export default function ContactMillenniumModal({ show, onHide }: ContactMillenniumModalProps) {
  const [formData, setFormData] = useState({
    subject: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const currentUser = useUserSession();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Make sure we're using the correct endpoint
      await api.client.post('/email/send', {
        subject: formData.subject,
        message: formData.message,
        clientId: currentUser?.clientCreatorId,
        includeClientDirector: true, // Add this flag to include the client director
      });

      toast.success('Your message has been sent successfully!');

      setFormData({ subject: '', message: '' });

      onHide();
    } catch (error) {
      console.error('Failed to send email:', error);
      toast.error('Failed to send your message. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal centered onHide={onHide} show={show} size="sm">
      <Modal.Header closeButton>
        <Modal.Title>Contact Millennium</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p className="mb-4">
          Enter the subject and your message for Millennium support. You will receive a response
          from Millennium's technical support team at the email address associated with your
          account.
        </p>

        <Form onSubmit={handleSubmit}>
          <Row>
            <Col md={12}>
              <Form.Group className="mb-3" controlId="formSubject">
                <Form.Label>Subject</Form.Label>
                <Form.Control
                  name="subject"
                  onChange={handleChange}
                  placeholder="Enter the subject"
                  required
                  type="text"
                  value={formData.subject}
                />
              </Form.Group>
            </Col>
          </Row>
          <Form.Group className="mb-3" controlId="formMessage">
            <Form.Label>Message</Form.Label>
            <Form.Control
              as="textarea"
              name="message"
              onChange={handleChange}
              placeholder="Type your message here. This will be sent to your dedicated Millennium team member."
              required
              rows={4}
              value={formData.message}
            />
          </Form.Group>
          <div className="d-flex justify-content-end gap-5">
            <Stack direction="row" spacing={2}>
              <Button className="me-2" onClick={onHide} variant="light">
                Cancel
              </Button>
              <Button disabled={isSubmitting} type="submit" variant="primary">
                {isSubmitting ? 'Sending...' : 'Send E-mail'}
              </Button>
            </Stack>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
}
