import { forwardRef, useState } from 'react';
import { tossError, tossSuccess } from 'utils/toastTosser';
import Modal from 'react-bootstrap/Modal';
import { createFilter, searchFilters } from 'services/filterService';
import { Filter, FilterData } from 'types/filter';
import { Stack, Typography } from '@mui/material';
import SecondaryButton from 'shared/muiComponents/SecondaryButton';
import StandardButton from 'shared/muiComponents/StandardButton';
import InputField from 'shared/muiComponents/InputField';

interface CreateModalProps {
  open: boolean;
  moduleId: number;
  filters: FilterData;
  onClose: () => void;
  onCreate: (filter: Filter) => void;
}

export default forwardRef(
  ({ open = false, moduleId, onClose, onCreate, filters }: CreateModalProps, ref) => {
    const [loading, setLoading] = useState(false);
    const [valid, setValid] = useState(false);
    const [invalid, setInvalid] = useState(false);
    const [name, setName] = useState<string | null>(null);

    const close = () => {
      setValid(false);
      setInvalid(false);
      setName(null);
      onClose();
    };

    const create = async () => {
      const filterFields = {
        title: name || '',
        moduleId,
        filters,
      };

      const newFilter = await createFilter(filterFields);

      if (newFilter) {
        onCreate({
          ...newFilter,
          name: newFilter.title,
        });
        tossSuccess('A new Filter Set created successfully!');
        close();
      } else tossError('Error creating a new Filter Set.');
    };

    return (
      <Modal ref={ref} centered className="bg-filter-set-create-modal" onHide={close} show={open}>
        <Modal.Body>
          <Stack direction="column" px={2} py={2} spacing={4}>
            <Typography m={2} variant="h2">
              Create Filter Set
            </Typography>

            <div>
              <InputField
                id="filterSetName"
                label="Filter Set Name"
                onChange={(value) => {
                  setName(value);
                  if (value.length === 0) {
                    setValid(false);
                    setInvalid(false);
                  } else {
                    // We need to check if the name already exists
                    searchFilters(moduleId, value, 'title').then((response) => {
                      const exacts = response.filter(({ title }) => title === value);
                      setValid(value.length > 0 && exacts.length === 0);
                      setInvalid(exacts.length > 0);
                    });
                  }
                }}
                placeholder="Enter name"
                required
                value={name || ''}
              />
              {invalid && (
                <div className="text-danger mt-1">
                  This name already exists. Please choose a different name.
                </div>
              )}
            </div>

            <Stack direction="row" justifyContent="flex-end" mt={2} spacing={2}>
              <SecondaryButton onClick={close}>Cancel</SecondaryButton>
              <StandardButton
                disabled={loading || invalid || (!valid && !invalid)}
                onClick={create}
              >
                Create
              </StandardButton>
            </Stack>
          </Stack>
        </Modal.Body>
      </Modal>
    );
  }
);
