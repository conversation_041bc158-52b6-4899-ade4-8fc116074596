import { useEffect, useRef, useState } from 'react';
import { Form, InputGroup } from 'react-bootstrap';
import TooltipWrapper from 'shared/muiComponents/TooltipWrapper';
import { Box } from '@mui/material';

interface TextInputProps {
  controlId: string;
  required?: boolean;
  label?: string;
  placeholder?: string;
  defaultValue?: string;
  type?: string;
  errors?: string;
  disabled?: boolean;
  onChange: (value: string) => void;
  text?: string;
  children?: (string | JSX.Element)[] | JSX.Element;
  maxLength?: number;
  minLength?: number;
  onBlur?: () => void;
  className?: string;
  textClass?: string;
}

export default function TextInput(props: TextInputProps) {
  const {
    controlId,
    required = false,
    label,
    placeholder,
    defaultValue,
    type = 'text',
    errors,
    disabled = false,
    onChange,
    text,
    children,
    maxLength,
    minLength,
    onBlur,
    className,
    textClass,
  } = props;

  const [value, setValue] = useState<string>(defaultValue || '');
  const [isTruncated, setIsTruncated] = useState(false);
  const spanRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (spanRef.current) {
      setIsTruncated(spanRef.current.scrollWidth > spanRef.current.clientWidth);
    }
  }, [defaultValue]);

  useEffect(() => {
    if (onChange) onChange(value);
  }, [value]);

  return (
    <Form.Group className={className} controlId={controlId}>
      {label && (
        <Form.Label>
          {label}
          {required ? ' *' : ''}
        </Form.Label>
      )}
      <TooltipWrapper text={isTruncated && disabled ? defaultValue || '' : ''}>
        <Box sx={{ width: '100%' }}>
          <InputGroup className={className}>
            <Form.Control
              className={className}
              defaultValue={defaultValue}
              disabled={disabled}
              isInvalid={!!errors}
              maxLength={maxLength}
              minLength={minLength}
              onBlur={onBlur}
              onChange={(e) => setValue(e.target.value)}
              placeholder={placeholder}
              required={required}
              type={type}
            />
            <InputGroup.Append>{children}</InputGroup.Append>
          </InputGroup>
          {/* Error message and text below the input field */}
          {Boolean(errors) && typeof errors === 'string' ? (
            <Form.Control.Feedback style={{ display: 'block' }} type="invalid">
              {errors}
            </Form.Control.Feedback>
          ) : (
            <Form.Text className={textClass} style={{ display: 'block' }}>
              {text}
            </Form.Text>
          )}
          {/* Hidden span for truncation check */}
          <span
            ref={spanRef}
            style={{
              position: 'absolute',
              visibility: 'hidden',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              width: '100%',
            }}
          >
            {defaultValue}
          </span>
        </Box>
      </TooltipWrapper>
    </Form.Group>
  );
}
