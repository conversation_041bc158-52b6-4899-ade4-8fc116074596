/* eslint-disable no-lonely-if */
import Form from 'react-bootstrap/Form';
import { useEffect, useState } from 'react';
import InputGroup from 'react-bootstrap/InputGroup';
import { Sliders } from 'react-feather';
import { ArrowDropDown, AttachMoney } from '@mui/icons-material';
import CurrencyInput from 'react-currency-input-field';
import Button from 'react-bootstrap/Button';

interface MoneyInputProps {
  controlId: string;
  label: string;
  defaultAmount: number | string;
  defaultStringAmount: string;
  defaultVaries: boolean;
  disabled?: boolean;
  errors: string;
  required?: boolean;
  hideRequiredIndicator?: boolean;
  text: string;
  canVary?: boolean;
  onAmountChange: (amount: number) => void;
  onVaryingAmountChange: (amount: string | null) => void;
  onVaryChange: (varies: boolean) => void;
  className?: string;
  textClass?: string;
  isFromPrograms?: boolean;
}

export default function MoneyInput(props: MoneyInputProps) {
  const {
    controlId,
    label,
    defaultAmount,
    defaultStringAmount,
    defaultVaries,
    disabled = false,
    errors,
    required = false,
    hideRequiredIndicator = false,
    text,
    canVary = true,
    onAmountChange,
    onVaryingAmountChange,
    onVaryChange,
    className,
    textClass,
    isFromPrograms = false,
  } = props;

  const [amount, setAmount] = useState<number | string>(0.0);
  const [stringAmount, setStringAmount] = useState<string | null>(null);
  const [varies, setVaries] = useState(false);

  useEffect(() => {
    if (defaultAmount !== undefined && defaultAmount !== null) {
      setAmount(defaultAmount);
    }
    if (defaultStringAmount !== undefined && defaultStringAmount !== null) {
      setStringAmount(defaultStringAmount);
    }
    if (defaultVaries !== undefined && defaultVaries !== null) {
      setVaries(defaultVaries);
    }
  }, [defaultAmount, defaultStringAmount, defaultVaries]);

  useEffect(() => {
    onVaryChange?.(varies);

    // Don't reset values when toggling between states
    if (varies) {
      // Keep the string value when toggling to varies mode
      if (!stringAmount && amount) {
        setStringAmount(amount.toString());
      }
    } else {
      // Keep the number value when toggling back to number mode
      if (!amount && stringAmount) {
        const parsedAmount = parseFloat(stringAmount);
        if (!isNaN(parsedAmount)) {
          setAmount(parsedAmount);
        }
      }
    }
  }, [varies]);

  useEffect(() => {
    if (!varies && amount) {
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
      onAmountChange?.(numAmount);
    }
  }, [amount]);

  useEffect(() => {
    if (varies && stringAmount) {
      onVaryingAmountChange?.(stringAmount);
    }
  }, [stringAmount]);

  const placeHolderComparison = defaultAmount === 'Multiple' || defaultStringAmount === 'Multiple';

  return (
    <Form.Group className={className} controlId={controlId}>
      <Form.Label>
        {label}
        {required && !hideRequiredIndicator ? ' *' : ''}
      </Form.Label>
      <InputGroup>
        {varies ? (
          <Form.Control
            className={`form-control${errors ? ' is-invalid' : ''}`}
            disabled={disabled}
            onChange={(e) => setStringAmount(e.target.value)}
            placeholder={placeHolderComparison ? 'Multiple' : 'Varies'}
            style={{ paddingRight: '40px' }}
            type="text"
            value={stringAmount || ''}
          />
        ) : (
          <CurrencyInput
            allowDecimals
            allowNegativeValue={false}
            className={`form-control${errors ? ' is-invalid' : ''}`}
            decimalSeparator="."
            decimalsLimit={2}
            disabled={disabled}
            groupSeparator=","
            onValueChange={(value) => setAmount(value || '0')}
            placeholder={placeHolderComparison ? 'Multiple' : '$0'}
            preserveCursor
            step={10}
            style={{ paddingRight: '40px' }}
            value={amount}
          />
        )}
        {canVary && (
          <div
            onClick={() => !disabled && setVaries((prevState) => !prevState)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                setVaries((prevState) => !prevState);
              }
            }}
            role="button"
            style={{
              position: 'absolute',
              top: '50%',
              right: '10px',
              transform: 'translateY(-50%)',
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
            }}
            tabIndex={0}
          >
            {!varies ? (
              <AttachMoney fontSize="small" sx={{ color: '#2C4474' }} />
            ) : (
              <Sliders size={16} />
            )}
          </div>
        )}
      </InputGroup>

      {errors ? (
        <div className="validation-errors">{errors}</div>
      ) : (
        <Form.Text className={textClass}>{text}</Form.Text>
      )}
    </Form.Group>
  );
}
