import Form from 'react-bootstrap/Form';
import { useEffect, useState } from 'react';
import InputGroup from 'react-bootstrap/InputGroup';
import { DollarSign, Sliders } from 'react-feather';
import CurrencyInput from 'react-currency-input-field';
import Button from 'react-bootstrap/Button';

interface MoneyInputProps {
  controlId: string;
  label: string;
  required?: boolean;
  defaultAmount?: number;
  defaultStringAmount?: string | null;
  canVary?: boolean;
  disabled?: boolean;
  errors?: string;
  text?: string;
  onAmountChange: (amount: number) => void;
  onVaryingAmountChange: (amount: string | null) => void;
  onVaryChange: (varies: boolean) => void;
  className?: string;
  defaultVaries?: boolean;
}

export default function MoneyInput(props: MoneyInputProps) {
  const {
    controlId,
    label,
    defaultAmount,
    defaultStringAmount,
    defaultVaries,
    disabled = false,
    errors,
    required = false,
    text,
    canVary = true,
    onAmountChange,
    onVaryingAmountChange,
    onVaryChange,
    className,
  } = props;

  const [amount, setAmount] = useState(defaultAmount ?? 0.0);
  const [stringAmount, setStringAmount] = useState(defaultStringAmount ?? null);
  const [varies, setVaries] = useState(defaultVaries ?? false);

  useEffect(() => {
    onVaryChange?.(varies);

    // Don't reset values when toggling between states
    if (varies) {
      // Keep the string value when toggling to varies mode
      if (!stringAmount && amount) {
        setStringAmount(amount.toString());
      }
    } else if (!amount && stringAmount) {
      const parsedAmount = parseFloat(stringAmount);
      if (!Number.isNaN(parsedAmount)) {
        setAmount(parsedAmount);
      }
    }
  }, [varies]);

  useEffect(() => {
    if (onAmountChange) onAmountChange(parseFloat(amount));
    if (onVaryingAmountChange && amount) onVaryingAmountChange(null);
  }, [amount]);

  useEffect(() => {
    if (onAmountChange && stringAmount) onAmountChange(0.0);
    if (onVaryingAmountChange) onVaryingAmountChange(stringAmount);
  }, [stringAmount]);

  const placeHolderComparison = defaultAmount === 'Multiple' || defaultStringAmount === 'Multiple';

  return (
    <Form.Group className={className} controlId={controlId}>
      <Form.Label>
        {label}
        {required ? ' *' : ''}
      </Form.Label>
      <InputGroup>
        {varies ? (
          <Form.Control
            className={`form-control${errors ? ' is-invalid' : ''}`}
            defaultValue={stringAmount}
            disabled={disabled}
            onChange={(e) => setStringAmount(e.target.value)}
            placeholder={placeHolderComparison ? 'Multiple' : 'Varies'}
            type="text"
          />
        ) : (
          <CurrencyInput
            allowDecimals
            allowNegativeValue={false}
            className={`form-control${errors ? ' is-invalid' : ''}`}
            decimalSeparator="."
            decimalsLimit={2}
            defaultValue={defaultAmount}
            disabled={disabled}
            groupSeparator=","
            onValueChange={(value) => setAmount(value)}
            placeholder={placeHolderComparison ? 'Multiple' : '$0'}
            step={10}
          />
        )}

        {canVary && (
          <div
            onClick={() => !disabled && setVaries((prevState) => !prevState)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                setVaries((prevState) => !prevState);
              }
            }}
            role="button"
            style={{
              position: 'absolute',
              top: '50%',
              right: '10px',
              transform: 'translateY(-50%)',
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
            }}
            tabIndex={0}
          >
            {!varies ? <DollarSign size={16} /> : <Sliders size={16} />}
          </div>
        )}
      </InputGroup>

      {errors ? <div className="validation-errors">{errors}</div> : <Form.Text>{text}</Form.Text>}
    </Form.Group>
  );
}
