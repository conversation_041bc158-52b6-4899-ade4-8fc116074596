import React, { useEffect, useState } from 'react';

interface CustomTooltipProps {
  hoveredCell: HTMLElement | null;
  tooltipText: string;
  show: boolean;
}

function CustomTooltip({ hoveredCell, tooltipText, show }: CustomTooltipProps) {
  const [shown, setShown] = useState(false);

  useEffect(() => {
    setShown(false);

    const delay = setTimeout(() => {
      setShown(Boolean(hoveredCell));
    }, 600);

    return () => clearTimeout(delay);
  }, [hoveredCell]);

  if (!hoveredCell) return null;

  const rect = hoveredCell.getBoundingClientRect();

  return (
    <div
      style={{
        position: 'absolute',
        top: rect.top - 70 + window.scrollY,
        left: rect.left - 70 + window.scrollX,
        color: 'white',
        fontFamily: 'Raleway',
        fontWeight: 600,
        padding: '5px 9px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 1000,
        backgroundColor: '#2C4474',
        pointerEvents: 'none',
      }}
    >
      {tooltipText}
    </div>
  );
}

export default CustomTooltip;
