import { ReactNode } from 'react';
import Add from '@mui/icons-material/Add';
import Button from '@mui/material/Button';

export default function StandardButton({
  onClick,
  children,
  disabled = false,
  startIcon = false,
}: {
  onClick: () => void;
  children: ReactNode;
  disabled?: boolean;
  startIcon?: boolean;
}) {
  return (
    <Button
      disabled={disabled}
      onClick={onClick}
      startIcon={startIcon ? <Add /> : ''}
      sx={{
        background: (theme) => theme.palette.primary.light,
        boxShadow: 'none',
        borderRadius: 2,
        color: 'white',
        textTransform: 'none',
        fontWeight: 500,
        fontSize: '0.8rem',
        lineHeight: 1,
        minHeight: 32,
        '&:hover': {
          background: (theme) => theme.palette.primary.main,
        },
      }}
      type="submit"
      variant="contained"
    >
      {children}
    </Button>
  );
}
