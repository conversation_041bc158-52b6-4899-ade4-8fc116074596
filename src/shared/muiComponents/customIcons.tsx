import { SvgIcon, SvgIconProps } from '@mui/material';

interface GenericIconProps extends SvgIconProps {
  svgPaths: { d: string }[];
  viewBoxSize?: number;
  size?: number;
}

interface IconProps extends SvgIconProps {
  size?: number;
}

function GenericIcon({ size = 16, viewBoxSize = 20, svgPaths, ...otherProps }: GenericIconProps) {
  return (
    <SvgIcon
      {...otherProps}
      style={{ width: `${size}px`, height: `${size}px` }}
      viewBox={`0 0 ${viewBoxSize} ${viewBoxSize}`}
    >
      {svgPaths.map((path) => (
        <path
          key={path.d}
          d={path.d}
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        />
      ))}
    </SvgIcon>
  );
}

export function DashboardIcon(props: IconProps) {
  const paths = [
    { d: 'M4.5 4.40723H10.5V10.4072H4.5V4.40723Z' },
    { d: 'M4.5 14.4072H10.5V20.4072H4.5V14.4072Z' },
    { d: 'M14.5 14.4072H20.5V20.4072H14.5V14.4072Z' },
    { d: 'M14.5 4.40723H20.5V10.4072H14.5V4.40723Z' },
  ];
  return <GenericIcon {...props} svgPaths={paths} viewBoxSize={25} />;
}

export function AwardsIcon(props: IconProps) {
  const paths = [
    {
      d: 'M10.5 13.4072C8.9087 13.4072 7.38258 12.7751 6.25736 11.6499C5.13214 10.5246 4.5 8.99853 4.5 7.40723C4.5 5.81593 5.13214 4.2898 6.25736 3.16459C7.38258 2.03937 8.9087 1.40723 10.5 1.40723C12.0913 1.40723 13.6174 2.03937 14.7426 3.16459C15.8679 4.2898 16.5 5.81593 16.5 7.40723C16.5 8.99853 15.8679 10.5246 14.7426 11.6499C13.6174 12.7751 12.0913 13.4072 10.5 13.4072ZM10.5 13.4072L13.9 19.2972L15.498 16.0642L19.096 16.2962L15.696 10.4072M5.30234 10.4072L1.90234 16.2972L5.50034 16.0642L7.09834 19.2962L10.4983 13.4072',
    },
  ];
  return <GenericIcon {...props} svgPaths={paths} viewBoxSize={25} />;
}

export function GrantsIcon(props: IconProps) {
  const paths = [
    {
      d: 'M9.5 5.40723H7.5C6.96957 5.40723 6.46086 5.61794 6.08579 5.99301C5.71071 6.36809 5.5 6.87679 5.5 7.40723V19.4072C5.5 19.9377 5.71071 20.4464 6.08579 20.8214C6.46086 21.1965 6.96957 21.4072 7.5 21.4072H17.5C18.0304 21.4072 18.5391 21.1965 18.9142 20.8214C19.2893 20.4464 19.5 19.9377 19.5 19.4072V7.40723C19.5 6.87679 19.2893 6.36809 18.9142 5.99301C18.5391 5.61794 18.0304 5.40723 17.5 5.40723H15.5M9.5 5.40723C9.5 4.87679 9.71071 4.36809 10.0858 3.99301C10.4609 3.61794 10.9696 3.40723 11.5 3.40723H13.5C14.0304 3.40723 14.5391 3.61794 14.9142 3.99301C15.2893 4.36809 15.5 4.87679 15.5 5.40723M9.5 5.40723C9.5 5.93766 9.71071 6.44637 10.0858 6.82144C10.4609 7.19651 10.9696 7.40723 11.5 7.40723H13.5C14.0304 7.40723 14.5391 7.19651 14.9142 6.82144C15.2893 6.44637 15.5 5.93766 15.5 5.40723M14.5 11.4072H12C11.6022 11.4072 11.2206 11.5653 10.9393 11.8466C10.658 12.1279 10.5 12.5094 10.5 12.9072C10.5 13.3051 10.658 13.6866 10.9393 13.9679C11.2206 14.2492 11.6022 14.4072 12 14.4072H13C13.3978 14.4072 13.7794 14.5653 14.0607 14.8466C14.342 15.1279 14.5 15.5094 14.5 15.9072C14.5 16.3051 14.342 16.6866 14.0607 16.9679C13.7794 17.2492 13.3978 17.4072 13 17.4072H10.5M12.5 17.4072V18.4072M12.5 10.4072V11.4072',
    },
  ];
  return <GenericIcon {...props} svgPaths={paths} viewBoxSize={25} />;
}

export function ClientsIcon(props: IconProps) {
  const paths = [
    {
      d: 'M3.5 1.40723H7.5L9.5 6.40723L7 7.90723C8.07096 10.0788 9.82847 11.8363 12 12.9072L13.5 10.4072L18.5 12.4072V16.4072C18.5 16.9377 18.2893 17.4464 17.9142 17.8214C17.5391 18.1965 17.0304 18.4072 16.5 18.4072C12.5993 18.1702 8.92015 16.5137 6.15683 13.7504C3.3935 10.9871 1.73705 7.30796 1.5 3.40723C1.5 2.87679 1.71071 2.36809 2.08579 1.99301C2.46086 1.61794 2.96957 1.40723 3.5 1.40723Z',
    },
  ];
  return <GenericIcon {...props} svgPaths={paths} viewBoxSize={20} />;
}

export function UsersIcon(props: IconProps) {
  const paths = [
    {
      d: 'M3.5 21.4072V19.4072C3.5 18.3464 3.92143 17.3289 4.67157 16.5788C5.42172 15.8287 6.43913 15.4072 7.5 15.4072H11.5C12.5609 15.4072 13.5783 15.8287 14.3284 16.5788C15.0786 17.3289 15.5 18.3464 15.5 19.4072V21.4072M16.5 3.53723C17.3604 3.75753 18.123 4.25793 18.6676 4.95954C19.2122 5.66115 19.5078 6.52406 19.5078 7.41223C19.5078 8.3004 19.2122 9.16331 18.6676 9.86492C18.123 10.5665 17.3604 11.0669 16.5 11.2872M21.5 21.4073V19.4073C21.4949 18.5244 21.1979 17.6681 20.6553 16.9717C20.1126 16.2753 19.3548 15.778 18.5 15.5573M5.5 7.40723C5.5 8.46809 5.92143 9.48551 6.67157 10.2357C7.42172 10.9858 8.43913 11.4072 9.5 11.4072C10.5609 11.4072 11.5783 10.9858 12.3284 10.2357C13.0786 9.48551 13.5 8.46809 13.5 7.40723C13.5 6.34636 13.0786 5.32894 12.3284 4.5788C11.5783 3.82865 10.5609 3.40723 9.5 3.40723C8.43913 3.40723 7.42172 3.82865 6.67157 4.5788C5.92143 5.32894 5.5 6.34636 5.5 7.40723Z',
    },
  ];
  return <GenericIcon {...props} svgPaths={paths} viewBoxSize={25} />;
}

export function ReportsIcon(props: IconProps) {
  const paths = [
    {
      d: 'M4.5 19.4072H20.5M4.5 15.4072L8.5 9.40723L12.5 11.4072L16.5 6.40723L20.5 10.4072',
    },
  ];
  return <GenericIcon {...props} svgPaths={paths} viewBoxSize={25} />;
}

export function EmptyAvatarIcon({ size = 38, ...otherProps }: IconProps) {
  const emptyAvatar =
    'data:image/png;base64,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';

  return (
    <SvgIcon
      {...otherProps}
      style={{ width: size, height: size }}
      sx={{
        background: (theme) => theme.palette.secondary.main,
        borderRadius: '50%',
        padding: '5px',
      }}
      viewBox="0 0 24 24"
    >
      <image height="24px" href={emptyAvatar} width="24px" x="0" y="0" />
    </SvgIcon>
  );
}
