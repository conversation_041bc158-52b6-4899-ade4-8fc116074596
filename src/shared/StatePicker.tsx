import { useState, useEffect } from 'react';
import Form from 'react-bootstrap/Form';
import Dropdown from 'react-bootstrap/Dropdown';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';

import { states, statesWithPriority } from '../constants/globalConstants';

interface StatePickerProps {
  controlId?: string;
  allStatesOption?: boolean;
  required?: boolean;
  disabled?: boolean;
  defaultValue?: string;
  label: string;
  onChange?: (newValue: string) => void;
  error?: string;
  className?: string;
}

export default function StatePicker(props: StatePickerProps) {
  const {
    controlId,
    allStatesOption = false,
    required = false,
    disabled,
    defaultValue,
    label,
    onChange,
    error,
    className,
  } = props;
  const [value, setValue] = useState<string>(defaultValue || '');

  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  useEffect(() => {
    onChange?.(value);
  }, [value]);

  useEffect(() => {
    if (defaultValue) setValue(defaultValue);
  }, [defaultValue]);

  // Use statesWithTopPriority instead of states
  const statesList = statesWithPriority;

  return (
    <Form.Group className={className} controlId={controlId}>
      <Form.Label>
        {label}
        {required ? ' *' : ''}
      </Form.Label>
      <Dropdown>
        <Dropdown.Toggle
          className="w-100 text-left form-control"
          disabled={disabled}
          variant="outline-select"
        >
          <Container fluid>
            <Row>
              {allStatesOption && value === 'all' && (
                <>
                  <Col className="state-abbreviation" xs={1} />
                  <Col xs={9}>All States</Col>
                </>
              )}

              <Col className="state-abbreviation" xs={1}>
                {value !== 'all' ? value : ''}
              </Col>
              <Col xs={9}>{statesList.find((s) => s.abbr === value)?.name || ''}</Col>

              {!value && 'Select state...'}
            </Row>
          </Container>
        </Dropdown.Toggle>

        <Dropdown.Menu className="w-100" style={{ maxHeight: '300px', overflowY: 'scroll' }}>
          {allStatesOption && (
            <Dropdown.Item onClick={() => setValue('all')}>
              <Container fluid>
                <Row>
                  <Col className="state-abbreviation" xs={1} />
                  <Col xs={9}>All States</Col>
                </Row>
              </Container>
            </Dropdown.Item>
          )}

          {statesList.map((currentState) => (
            <Dropdown.Item key={currentState.name} onClick={() => setValue(currentState.abbr)}>
              <Container fluid>
                <Row>
                  <Col className="state-abbreviation" xs={1}>
                    {currentState.abbr}
                  </Col>
                  <Col xs={9}>{currentState.name}</Col>
                </Row>
              </Container>
            </Dropdown.Item>
          ))}
        </Dropdown.Menu>
      </Dropdown>

      {error && <Form.Control.Feedback type="invalid">{error}</Form.Control.Feedback>}
    </Form.Group>
  );
}
