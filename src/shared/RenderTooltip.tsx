import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import { Placement } from 'react-bootstrap/Overlay';
import { OverlayDelay } from 'react-bootstrap/OverlayTrigger';
import 'assets/scss/RenderTooltip.scss';

interface RenderTooltipProps {
  tooltipData?: string[];
  choices?: string[];
  placement?: Placement;
  delay: OverlayDelay;
  children?: JSX.Element;
}

export default function RenderTooltip(props: RenderTooltipProps) {
  const { tooltipData, choices, placement, delay, children } = props;

  const renderTip = (rest) => (
    <Tooltip id="tooltip" {...rest}>
      <div
        style={{
          backgroundColor: '#2C4474',
          fontSize: '12px',
          fontWeight: 600,
          padding: '8px 12px',
          fontFamily: 'Raleway',
          borderRadius: '4px',
          lineHeight: 1.4,
          maxWidth: '300px',
          wordWrap: 'break-word',
          color: 'white',
        }}
      >
        <span>Entries:</span>
        <ul
          style={{
            margin: 0,
            padding: '4px 0 0 20px',
          }}
        >
          {tooltipData?.map((tip, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <li
              // eslint-disable-next-line react/no-array-index-key
              key={`tip-${index}`}
              className="render-custom-tooltip__list-item"
              style={{
                marginBottom: '4px',
                fontSize: '12px',
                fontWeight: 600,
              }}
            >
              {choices ? choices[tip] : tip}
            </li>
          ))}
        </ul>
      </div>
    </Tooltip>
  );

  return tooltipData && tooltipData.length > 1 ? (
    <OverlayTrigger delay={delay} overlay={renderTip} placement={placement}>
      <div className="render-custom-tooltip">{children}</div>
    </OverlayTrigger>
  ) : (
    children
  );
}
