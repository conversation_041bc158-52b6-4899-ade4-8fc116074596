import { useState, useEffect } from 'react';
import Form from 'react-bootstrap/Form';
import Dropdown from 'react-bootstrap/Dropdown';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import { applicationDates } from '../constants/globalConstants';

interface DateTypePickerProps {
  controlId: string;
  defaultValue: number;
  label: string;
  onChange: (value: number) => void;
}

export default function DateTypePicker({
  controlId,
  defaultValue = 0,
  label,
  onChange,
}: DateTypePickerProps) {
  const [value, setValue] = useState(defaultValue || 0);

  useEffect(() => {
    if (onChange) onChange(value || 0);
  }, [value]);

  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  return (
    <Form.Group controlId={controlId}>
      <Form.Label>{label}</Form.Label>
      <Dropdown>
        <Dropdown.Toggle className="w-100 text-left" variant="outline-select">
          <Container fluid>
            <Row>
              {Number.isInteger(value) && <Col xs={9}>{applicationDates[value].name}</Col>}

              {!Number.isInteger(value) && !value && 'Select a date type...'}
            </Row>
          </Container>
        </Dropdown.Toggle>

        <Dropdown.Menu className="w-100" style={{ maxHeight: '300px', overflowY: 'scroll' }}>
          {applicationDates.map((current, i) => (
            <Dropdown.Item key={current.name} onClick={() => setValue(i)}>
              <Container fluid>
                <Row>
                  <Col xs={9}>{current.name}</Col>
                </Row>
              </Container>
            </Dropdown.Item>
          ))}
        </Dropdown.Menu>
      </Dropdown>
    </Form.Group>
  );
}
