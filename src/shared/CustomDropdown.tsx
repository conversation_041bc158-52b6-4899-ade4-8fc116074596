import { Dispatch, SetStateAction, useCallback, useEffect, useState } from 'react';
import { Button, Col } from 'react-bootstrap';

import { Filters } from 'components/reports/filters/ReportsFiltersUI';

function CustomSelectList({
  changeInputComponent,
  options,
  alreadyUsed,
  name,
}: {
  changeInputComponent: (inputOption: string, inputName: string) => void;
  options: string[];
  alreadyUsed: string[];
  name: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const selectDropdown = () => setIsOpen((prevState) => !prevState);

  return (
    <ul className="custom-dropdown__select">
      {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events,jsx-a11y/no-noninteractive-element-interactions */}
      <li
        className={`custom-dropdown__select--button ${
          isOpen ? 'custom-dropdown__select--button--active' : ''
        }`}
        onClick={selectDropdown}
      >
        {name}
      </li>
      {isOpen &&
        options?.map(
          (option) =>
            !alreadyUsed?.includes(option) && (
              // eslint-disable-next-line jsx-a11y/click-events-have-key-events,jsx-a11y/no-noninteractive-element-interactions
              <li
                key={option}
                className={`${
                  isOpen ? 'custom-dropdown_select--active' : 'custom-dropdown_select--disabled'
                }
                ${alreadyUsed?.includes(option) ? 'disabled' : null}`}
                onClick={() =>
                  alreadyUsed?.includes(option) ? null : changeInputComponent(option, name)
                }
              >
                {option}
              </li>
            )
        )}
    </ul>
  );
}

export default function CustomDropdown({
  label,
  Components,
  activeTab,
  filters,
  setFilters,
  options = ['Funder', 'Client', 'Program'],
}: {
  label: string;
  Components: JSX.Element[];
  activeTab?: number;
  filters?: Filters;
  setFilters?: Dispatch<SetStateAction<Filters>>;
  options?: string[];
}) {
  const [isActive, setIsActive] = useState(false);
  const [alreadyUsed, setIsAlreadyUsed] = useState<string[]>([]);
  const [count, setCount] = useState(0);

  const toggle = () => setIsActive((prevState) => !prevState);

  const inputFromFilter = (filter: Filters) => {
    if (!filter) return;

    const newOptions = [];

    if ((filter?.clients?.length || alreadyUsed?.includes('Client')) && activeTab === 1)
      newOptions.push('Client');
    if (filter?.funders?.length || alreadyUsed?.includes('Funder')) newOptions.push('Funder');
    if (filter?.programNames?.length || alreadyUsed?.includes('Program'))
      newOptions.push('Program');

    if (!newOptions.length && !alreadyUsed?.length) setIsAlreadyUsed(['Funder']);

    if (newOptions.length) {
      setIsAlreadyUsed(newOptions);
      setCount(newOptions.length);
    }
  };

  const clearInputFilter = (name: string) => {
    if (!name) return;

    if (name === 'Program') setFilters?.({ ...filters, programNames: [] });
    if (name === 'Funder') setFilters?.({ ...filters, funders: [] });
    if (name === 'Client') setFilters?.({ ...filters, clients: [] });
  };

  const changeInputComponent = useCallback(
    (value: string, name: string) => {
      if (!value) return;

      if (count >= 1) {
        if (alreadyUsed.includes(name)) {
          const usedOptions = [...alreadyUsed];
          usedOptions[alreadyUsed?.indexOf(name)] = value;
          clearInputFilter(name);
          setIsAlreadyUsed(usedOptions);
        } else {
          setIsAlreadyUsed([...alreadyUsed, value]);
        }
      } else {
        clearInputFilter(name);
        setIsAlreadyUsed([value]);
      }
    },
    [count, alreadyUsed]
  );

  const addInputElement = () => {
    if (options[alreadyUsed.length] !== undefined) {
      const unusedOptions = options.filter((option) => !alreadyUsed?.includes(option));
      const option = Array.isArray(unusedOptions) ? unusedOptions[0] : unusedOptions;
      setIsAlreadyUsed([...alreadyUsed, option]);
    }
    setCount(() => count + 1);
  };

  const removeInput = (name: string) => {
    const componentFilters = Components?.find((component) => component?.type?.name?.includes(name));

    if (componentFilters?.props?.filters?.length >= 1) componentFilters?.props?.onChange([]);

    setIsAlreadyUsed(alreadyUsed?.filter((option) => option !== name));
    setCount(() => count - 1);
  };

  useEffect(() => {
    if (filters) {
      inputFromFilter(filters);
    } else {
      setIsAlreadyUsed(activeTab === 1 ? ['Client'] : ['Funder']);
    }
  }, [filters, activeTab]);

  const componentItems =
    Array.isArray(alreadyUsed) &&
    alreadyUsed?.map((name) => {
      return (
        <div key={name} className="custom-dropdown__item">
          {(Components || []).filter((component) => component.type.name.includes(name))}
          <CustomSelectList
            alreadyUsed={alreadyUsed}
            changeInputComponent={changeInputComponent}
            name={name}
            options={options}
          />
          {alreadyUsed?.length > 1 && (
            <Button color="primary-blue" onClick={() => removeInput(name)} variant="primary-blue">
              x
            </Button>
          )}
        </div>
      );
    });

  return (
    <div className={`custom-dropdown custom-dropdown--${label}`}>
      <input
        className={`custom-dropdown_header ${
          isActive ? 'custom-dropdown_header--active' : 'custom-dropdown_header--disabled'
        }`}
        onClick={toggle}
        type="button"
        value={label.replace(/-/, ' ')}
      />
      <div
        className={`custom-dropdown__list ${
          isActive ? 'custom-dropdown__list--active' : 'custom-dropdown__list--disabled'
        }`}
      >
        <Col className="custom-dropdown__inputs">
          {label && label === 'Search' ? (
            <div className="custom-dropdown__items">
              {componentItems}
              <div className="custom-dropdown__bottom">
                {(Components || []).filter((component) => component?.type?.name?.includes('Date'))}
                {alreadyUsed.length < Components.length - 1 && (
                  <Button color="primary-green" onClick={addInputElement} variant="primary-green">
                    +
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div>{Components}</div>
          )}
        </Col>
      </div>
    </div>
  );
}
