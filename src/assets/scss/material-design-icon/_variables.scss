$mdi-filename: "materialdesignicons";
$mdi-font-name: "Material Design Icons";
$mdi-font-family: "materialdesignicons";
$mdi-font-weight: "regular";
$mdi-font-path: "../../fonts/material-design-icon" !default;
$mdi-font-size-base: 24px !default;
$mdi-css-prefix: mdi !default;
$mdi-version: "3.6.95" !default;
$mdi-icons: (
    "access-point": f002,
    "access-point-network": f003,
    "access-point-network-off": fbbd,
    "account": f004,
    "account-alert": f005,
    "account-alert-outline": fb2c,
    "account-arrow-left": fb2d,
    "account-arrow-left-outline": fb2e,
    "account-arrow-right": fb2f,
    "account-arrow-right-outline": fb30,
    "account-badge": fd83,
    "account-badge-alert": fd84,
    "account-badge-alert-outline": fd85,
    "account-badge-horizontal": fdf0,
    "account-badge-horizontal-outline": fdf1,
    "account-badge-outline": fd86,
    "account-box": f006,
    "account-box-multiple": f933,
    "account-box-outline": f007,
    "account-card-details": f5d2,
    "account-card-details-outline": fd87,
    "account-check": f008,
    "account-check-outline": fbbe,
    "account-child": fa88,
    "account-child-circle": fa89,
    "account-circle": f009,
    "account-circle-outline": fb31,
    "account-clock": fb32,
    "account-clock-outline": fb33,
    "account-convert": f00a,
    "account-details": f631,
    "account-edit": f6bb,
    "account-group": f848,
    "account-group-outline": fb34,
    "account-heart": f898,
    "account-heart-outline": fbbf,
    "account-key": f00b,
    "account-key-outline": fbc0,
    "account-minus": f00d,
    "account-minus-outline": faeb,
    "account-multiple": f00e,
    "account-multiple-check": f8c4,
    "account-multiple-minus": f5d3,
    "account-multiple-minus-outline": fbc1,
    "account-multiple-outline": f00f,
    "account-multiple-plus": f010,
    "account-multiple-plus-outline": f7ff,
    "account-network": f011,
    "account-network-outline": fbc2,
    "account-off": f012,
    "account-off-outline": fbc3,
    "account-outline": f013,
    "account-plus": f014,
    "account-plus-outline": f800,
    "account-question": fb35,
    "account-question-outline": fb36,
    "account-remove": f015,
    "account-remove-outline": faec,
    "account-search": f016,
    "account-search-outline": f934,
    "account-settings": f630,
    "account-star": f017,
    "account-star-outline": fbc4,
    "account-supervisor": fa8a,
    "account-supervisor-circle": fa8b,
    "account-switch": f019,
    "account-tie": fcbf,
    "accusoft": f849,
    "adchoices": fd1e,
    "adjust": f01a,
    "adobe": f935,
    "air-conditioner": f01b,
    "air-filter": fd1f,
    "air-horn": fd88,
    "air-purifier": fd20,
    "airbag": fbc5,
    "airballoon": f01c,
    "airplane": f01d,
    "airplane-landing": f5d4,
    "airplane-off": f01e,
    "airplane-takeoff": f5d5,
    "airplay": f01f,
    "airport": f84a,
    "alarm": f020,
    "alarm-bell": f78d,
    "alarm-check": f021,
    "alarm-light": f78e,
    "alarm-light-outline": fbc6,
    "alarm-multiple": f022,
    "alarm-off": f023,
    "alarm-plus": f024,
    "alarm-snooze": f68d,
    "album": f025,
    "alert": f026,
    "alert-box": f027,
    "alert-box-outline": fcc0,
    "alert-circle": f028,
    "alert-circle-outline": f5d6,
    "alert-decagram": f6bc,
    "alert-decagram-outline": fcc1,
    "alert-octagon": f029,
    "alert-octagon-outline": fcc2,
    "alert-octagram": f766,
    "alert-octagram-outline": fcc3,
    "alert-outline": f02a,
    "alien": f899,
    "all-inclusive": f6bd,
    "alpha": f02b,
    "alpha-a": 0041,
    "alpha-a-box": faed,
    "alpha-a-box-outline": fbc7,
    "alpha-a-circle": fbc8,
    "alpha-a-circle-outline": fbc9,
    "alpha-b": 0042,
    "alpha-b-box": faee,
    "alpha-b-box-outline": fbca,
    "alpha-b-circle": fbcb,
    "alpha-b-circle-outline": fbcc,
    "alpha-c": 0043,
    "alpha-c-box": faef,
    "alpha-c-box-outline": fbcd,
    "alpha-c-circle": fbce,
    "alpha-c-circle-outline": fbcf,
    "alpha-d": 0044,
    "alpha-d-box": faf0,
    "alpha-d-box-outline": fbd0,
    "alpha-d-circle": fbd1,
    "alpha-d-circle-outline": fbd2,
    "alpha-e": 0045,
    "alpha-e-box": faf1,
    "alpha-e-box-outline": fbd3,
    "alpha-e-circle": fbd4,
    "alpha-e-circle-outline": fbd5,
    "alpha-f": 0046,
    "alpha-f-box": faf2,
    "alpha-f-box-outline": fbd6,
    "alpha-f-circle": fbd7,
    "alpha-f-circle-outline": fbd8,
    "alpha-g": 0047,
    "alpha-g-box": faf3,
    "alpha-g-box-outline": fbd9,
    "alpha-g-circle": fbda,
    "alpha-g-circle-outline": fbdb,
    "alpha-h": 0048,
    "alpha-h-box": faf4,
    "alpha-h-box-outline": fbdc,
    "alpha-h-circle": fbdd,
    "alpha-h-circle-outline": fbde,
    "alpha-i": 0049,
    "alpha-i-box": faf5,
    "alpha-i-box-outline": fbdf,
    "alpha-i-circle": fbe0,
    "alpha-i-circle-outline": fbe1,
    "alpha-j": 004A,
    "alpha-j-box": faf6,
    "alpha-j-box-outline": fbe2,
    "alpha-j-circle": fbe3,
    "alpha-j-circle-outline": fbe4,
    "alpha-k": 004B,
    "alpha-k-box": faf7,
    "alpha-k-box-outline": fbe5,
    "alpha-k-circle": fbe6,
    "alpha-k-circle-outline": fbe7,
    "alpha-l": 004C,
    "alpha-l-box": faf8,
    "alpha-l-box-outline": fbe8,
    "alpha-l-circle": fbe9,
    "alpha-l-circle-outline": fbea,
    "alpha-m": 004D,
    "alpha-m-box": faf9,
    "alpha-m-box-outline": fbeb,
    "alpha-m-circle": fbec,
    "alpha-m-circle-outline": fbed,
    "alpha-n": 004E,
    "alpha-n-box": fafa,
    "alpha-n-box-outline": fbee,
    "alpha-n-circle": fbef,
    "alpha-n-circle-outline": fbf0,
    "alpha-o": 004F,
    "alpha-o-box": fafb,
    "alpha-o-box-outline": fbf1,
    "alpha-o-circle": fbf2,
    "alpha-o-circle-outline": fbf3,
    "alpha-p": 0050,
    "alpha-p-box": fafc,
    "alpha-p-box-outline": fbf4,
    "alpha-p-circle": fbf5,
    "alpha-p-circle-outline": fbf6,
    "alpha-q": 0051,
    "alpha-q-box": fafd,
    "alpha-q-box-outline": fbf7,
    "alpha-q-circle": fbf8,
    "alpha-q-circle-outline": fbf9,
    "alpha-r": 0052,
    "alpha-r-box": fafe,
    "alpha-r-box-outline": fbfa,
    "alpha-r-circle": fbfb,
    "alpha-r-circle-outline": fbfc,
    "alpha-s": 0053,
    "alpha-s-box": faff,
    "alpha-s-box-outline": fbfd,
    "alpha-s-circle": fbfe,
    "alpha-s-circle-outline": fbff,
    "alpha-t": 0054,
    "alpha-t-box": fb00,
    "alpha-t-box-outline": fc00,
    "alpha-t-circle": fc01,
    "alpha-t-circle-outline": fc02,
    "alpha-u": 0055,
    "alpha-u-box": fb01,
    "alpha-u-box-outline": fc03,
    "alpha-u-circle": fc04,
    "alpha-u-circle-outline": fc05,
    "alpha-v": 0056,
    "alpha-v-box": fb02,
    "alpha-v-box-outline": fc06,
    "alpha-v-circle": fc07,
    "alpha-v-circle-outline": fc08,
    "alpha-w": 0057,
    "alpha-w-box": fb03,
    "alpha-w-box-outline": fc09,
    "alpha-w-circle": fc0a,
    "alpha-w-circle-outline": fc0b,
    "alpha-x": 0058,
    "alpha-x-box": fb04,
    "alpha-x-box-outline": fc0c,
    "alpha-x-circle": fc0d,
    "alpha-x-circle-outline": fc0e,
    "alpha-y": 0059,
    "alpha-y-box": fb05,
    "alpha-y-box-outline": fc0f,
    "alpha-y-circle": fc10,
    "alpha-y-circle-outline": fc11,
    "alpha-z": 005A,
    "alpha-z-box": fb06,
    "alpha-z-box-outline": fc12,
    "alpha-z-circle": fc13,
    "alpha-z-circle-outline": fc14,
    "alphabetical": f02c,
    "altimeter": f5d7,
    "amazon": f02d,
    "amazon-alexa": f8c5,
    "amazon-drive": f02e,
    "ambulance": f02f,
    "ammunition": fcc4,
    "ampersand": fa8c,
    "amplifier": f030,
    "anchor": f031,
    "android": f032,
    "android-auto": fa8d,
    "android-debug-bridge": f033,
    "android-head": f78f,
    "android-messages": fd21,
    "android-studio": f034,
    "angle-acute": f936,
    "angle-obtuse": f937,
    "angle-right": f938,
    "angular": f6b1,
    "angularjs": f6be,
    "animation": f5d8,
    "animation-outline": fa8e,
    "animation-play": f939,
    "animation-play-outline": fa8f,
    "anvil": f89a,
    "apple": f035,
    "apple-finder": f036,
    "apple-icloud": f038,
    "apple-ios": f037,
    "apple-keyboard-caps": f632,
    "apple-keyboard-command": f633,
    "apple-keyboard-control": f634,
    "apple-keyboard-option": f635,
    "apple-keyboard-shift": f636,
    "apple-safari": f039,
    "application": f614,
    "application-export": fd89,
    "application-import": fd8a,
    "apps": f03b,
    "apps-box": fd22,
    "arch": f8c6,
    "archive": f03c,
    "arrange-bring-forward": f03d,
    "arrange-bring-to-front": f03e,
    "arrange-send-backward": f03f,
    "arrange-send-to-back": f040,
    "arrow-all": f041,
    "arrow-bottom-left": f042,
    "arrow-bottom-left-bold-outline": f9b6,
    "arrow-bottom-left-thick": f9b7,
    "arrow-bottom-right": f043,
    "arrow-bottom-right-bold-outline": f9b8,
    "arrow-bottom-right-thick": f9b9,
    "arrow-collapse": f615,
    "arrow-collapse-all": f044,
    "arrow-collapse-down": f791,
    "arrow-collapse-horizontal": f84b,
    "arrow-collapse-left": f792,
    "arrow-collapse-right": f793,
    "arrow-collapse-up": f794,
    "arrow-collapse-vertical": f84c,
    "arrow-decision": f9ba,
    "arrow-decision-auto": f9bb,
    "arrow-decision-auto-outline": f9bc,
    "arrow-decision-outline": f9bd,
    "arrow-down": f045,
    "arrow-down-bold": f72d,
    "arrow-down-bold-box": f72e,
    "arrow-down-bold-box-outline": f72f,
    "arrow-down-bold-circle": f047,
    "arrow-down-bold-circle-outline": f048,
    "arrow-down-bold-hexagon-outline": f049,
    "arrow-down-bold-outline": f9be,
    "arrow-down-box": f6bf,
    "arrow-down-circle": fcb7,
    "arrow-down-circle-outline": fcb8,
    "arrow-down-drop-circle": f04a,
    "arrow-down-drop-circle-outline": f04b,
    "arrow-down-thick": f046,
    "arrow-expand": f616,
    "arrow-expand-all": f04c,
    "arrow-expand-down": f795,
    "arrow-expand-horizontal": f84d,
    "arrow-expand-left": f796,
    "arrow-expand-right": f797,
    "arrow-expand-up": f798,
    "arrow-expand-vertical": f84e,
    "arrow-left": f04d,
    "arrow-left-bold": f730,
    "arrow-left-bold-box": f731,
    "arrow-left-bold-box-outline": f732,
    "arrow-left-bold-circle": f04f,
    "arrow-left-bold-circle-outline": f050,
    "arrow-left-bold-hexagon-outline": f051,
    "arrow-left-bold-outline": f9bf,
    "arrow-left-box": f6c0,
    "arrow-left-circle": fcb9,
    "arrow-left-circle-outline": fcba,
    "arrow-left-drop-circle": f052,
    "arrow-left-drop-circle-outline": f053,
    "arrow-left-right-bold-outline": f9c0,
    "arrow-left-thick": f04e,
    "arrow-right": f054,
    "arrow-right-bold": f733,
    "arrow-right-bold-box": f734,
    "arrow-right-bold-box-outline": f735,
    "arrow-right-bold-circle": f056,
    "arrow-right-bold-circle-outline": f057,
    "arrow-right-bold-hexagon-outline": f058,
    "arrow-right-bold-outline": f9c1,
    "arrow-right-box": f6c1,
    "arrow-right-circle": fcbb,
    "arrow-right-circle-outline": fcbc,
    "arrow-right-drop-circle": f059,
    "arrow-right-drop-circle-outline": f05a,
    "arrow-right-thick": f055,
    "arrow-split-horizontal": f93a,
    "arrow-split-vertical": f93b,
    "arrow-top-left": f05b,
    "arrow-top-left-bold-outline": f9c2,
    "arrow-top-left-thick": f9c3,
    "arrow-top-right": f05c,
    "arrow-top-right-bold-outline": f9c4,
    "arrow-top-right-thick": f9c5,
    "arrow-up": f05d,
    "arrow-up-bold": f736,
    "arrow-up-bold-box": f737,
    "arrow-up-bold-box-outline": f738,
    "arrow-up-bold-circle": f05f,
    "arrow-up-bold-circle-outline": f060,
    "arrow-up-bold-hexagon-outline": f061,
    "arrow-up-bold-outline": f9c6,
    "arrow-up-box": f6c2,
    "arrow-up-circle": fcbd,
    "arrow-up-circle-outline": fcbe,
    "arrow-up-down-bold-outline": f9c7,
    "arrow-up-drop-circle": f062,
    "arrow-up-drop-circle-outline": f063,
    "arrow-up-thick": f05e,
    "artist": f802,
    "artist-outline": fcc5,
    "artstation": fb37,
    "aspect-ratio": fa23,
    "assistant": f064,
    "asterisk": f6c3,
    "at": f065,
    "atlassian": f803,
    "atm": fd23,
    "atom": f767,
    "attachment": f066,
    "audio-video": f93c,
    "audiobook": f067,
    "augmented-reality": f84f,
    "auto-fix": f068,
    "auto-upload": f069,
    "autorenew": f06a,
    "av-timer": f06b,
    "aws": fdf2,
    "axe": f8c7,
    "axis": fd24,
    "axis-arrow": fd25,
    "axis-arrow-lock": fd26,
    "axis-lock": fd27,
    "axis-x-arrow": fd28,
    "axis-x-arrow-lock": fd29,
    "axis-x-rotate-clockwise": fd2a,
    "axis-x-rotate-counterclockwise": fd2b,
    "axis-x-y-arrow-lock": fd2c,
    "axis-y-arrow": fd2d,
    "axis-y-arrow-lock": fd2e,
    "axis-y-rotate-clockwise": fd2f,
    "axis-y-rotate-counterclockwise": fd30,
    "axis-z-arrow": fd31,
    "axis-z-arrow-lock": fd32,
    "axis-z-rotate-clockwise": fd33,
    "axis-z-rotate-counterclockwise": fd34,
    "azure": f804,
    "babel": fa24,
    "baby": f06c,
    "baby-buggy": f68e,
    "backburger": f06d,
    "backspace": f06e,
    "backspace-outline": fb38,
    "backup-restore": f06f,
    "badminton": f850,
    "bag-personal": fdf3,
    "bag-personal-off": fdf4,
    "bag-personal-off-outline": fdf5,
    "bag-personal-outline": fdf6,
    "balloon": fa25,
    "ballot": f9c8,
    "ballot-outline": f9c9,
    "ballot-recount": fc15,
    "ballot-recount-outline": fc16,
    "bandage": fd8b,
    "bandcamp": f674,
    "bank": f070,
    "bank-minus": fd8c,
    "bank-plus": fd8d,
    "bank-remove": fd8e,
    "bank-transfer": fa26,
    "bank-transfer-in": fa27,
    "bank-transfer-out": fa28,
    "barcode": f071,
    "barcode-scan": f072,
    "barley": f073,
    "barley-off": fb39,
    "barn": fb3a,
    "barrel": f074,
    "baseball": f851,
    "baseball-bat": f852,
    "basecamp": f075,
    "basket": f076,
    "basket-fill": f077,
    "basket-unfill": f078,
    "basketball": f805,
    "basketball-hoop": fc17,
    "basketball-hoop-outline": fc18,
    "bat": fb3b,
    "battery": f079,
    "battery-10": f07a,
    "battery-10-bluetooth": f93d,
    "battery-20": f07b,
    "battery-20-bluetooth": f93e,
    "battery-30": f07c,
    "battery-30-bluetooth": f93f,
    "battery-40": f07d,
    "battery-40-bluetooth": f940,
    "battery-50": f07e,
    "battery-50-bluetooth": f941,
    "battery-60": f07f,
    "battery-60-bluetooth": f942,
    "battery-70": f080,
    "battery-70-bluetooth": f943,
    "battery-80": f081,
    "battery-80-bluetooth": f944,
    "battery-90": f082,
    "battery-90-bluetooth": f945,
    "battery-alert": f083,
    "battery-alert-bluetooth": f946,
    "battery-bluetooth": f947,
    "battery-bluetooth-variant": f948,
    "battery-charging": f084,
    "battery-charging-10": f89b,
    "battery-charging-100": f085,
    "battery-charging-20": f086,
    "battery-charging-30": f087,
    "battery-charging-40": f088,
    "battery-charging-50": f89c,
    "battery-charging-60": f089,
    "battery-charging-70": f89d,
    "battery-charging-80": f08a,
    "battery-charging-90": f08b,
    "battery-charging-outline": f89e,
    "battery-charging-wireless": f806,
    "battery-charging-wireless-10": f807,
    "battery-charging-wireless-20": f808,
    "battery-charging-wireless-30": f809,
    "battery-charging-wireless-40": f80a,
    "battery-charging-wireless-50": f80b,
    "battery-charging-wireless-60": f80c,
    "battery-charging-wireless-70": f80d,
    "battery-charging-wireless-80": f80e,
    "battery-charging-wireless-90": f80f,
    "battery-charging-wireless-alert": f810,
    "battery-charging-wireless-outline": f811,
    "battery-minus": f08c,
    "battery-negative": f08d,
    "battery-outline": f08e,
    "battery-plus": f08f,
    "battery-positive": f090,
    "battery-unknown": f091,
    "battery-unknown-bluetooth": f949,
    "battlenet": fb3c,
    "beach": f092,
    "beaker": fcc6,
    "beaker-outline": f68f,
    "beats": f097,
    "bed-empty": f89f,
    "beer": f098,
    "behance": f099,
    "bell": f09a,
    "bell-alert": fd35,
    "bell-circle": fd36,
    "bell-circle-outline": fd37,
    "bell-off": f09b,
    "bell-off-outline": fa90,
    "bell-outline": f09c,
    "bell-plus": f09d,
    "bell-plus-outline": fa91,
    "bell-ring": f09e,
    "bell-ring-outline": f09f,
    "bell-sleep": f0a0,
    "bell-sleep-outline": fa92,
    "beta": f0a1,
    "betamax": f9ca,
    "biathlon": fdf7,
    "bible": f0a2,
    "bike": f0a3,
    "billiards": fb3d,
    "billiards-rack": fb3e,
    "bing": f0a4,
    "binoculars": f0a5,
    "bio": f0a6,
    "biohazard": f0a7,
    "bitbucket": f0a8,
    "bitcoin": f812,
    "black-mesa": f0a9,
    "blackberry": f0aa,
    "blender": fcc7,
    "blender-software": f0ab,
    "blinds": f0ac,
    "block-helper": f0ad,
    "blogger": f0ae,
    "blood-bag": fcc8,
    "bluetooth": f0af,
    "bluetooth-audio": f0b0,
    "bluetooth-connect": f0b1,
    "bluetooth-off": f0b2,
    "bluetooth-settings": f0b3,
    "bluetooth-transfer": f0b4,
    "blur": f0b5,
    "blur-linear": f0b6,
    "blur-off": f0b7,
    "blur-radial": f0b8,
    "bolnisi-cross": fcc9,
    "bolt": fd8f,
    "bomb": f690,
    "bomb-off": f6c4,
    "bone": f0b9,
    "book": f0ba,
    "book-lock": f799,
    "book-lock-open": f79a,
    "book-minus": f5d9,
    "book-multiple": f0bb,
    "book-multiple-minus": fa93,
    "book-multiple-plus": fa94,
    "book-multiple-remove": fa95,
    "book-multiple-variant": f0bc,
    "book-open": f0bd,
    "book-open-outline": fb3f,
    "book-open-page-variant": f5da,
    "book-open-variant": f0be,
    "book-outline": fb40,
    "book-plus": f5db,
    "book-remove": fa96,
    "book-variant": f0bf,
    "bookmark": f0c0,
    "bookmark-check": f0c1,
    "bookmark-minus": f9cb,
    "bookmark-minus-outline": f9cc,
    "bookmark-multiple": fdf8,
    "bookmark-multiple-outline": fdf9,
    "bookmark-music": f0c2,
    "bookmark-off": f9cd,
    "bookmark-off-outline": f9ce,
    "bookmark-outline": f0c3,
    "bookmark-plus": f0c5,
    "bookmark-plus-outline": f0c4,
    "bookmark-remove": f0c6,
    "boombox": f5dc,
    "bootstrap": f6c5,
    "border-all": f0c7,
    "border-all-variant": f8a0,
    "border-bottom": f0c8,
    "border-bottom-variant": f8a1,
    "border-color": f0c9,
    "border-horizontal": f0ca,
    "border-inside": f0cb,
    "border-left": f0cc,
    "border-left-variant": f8a2,
    "border-none": f0cd,
    "border-none-variant": f8a3,
    "border-outside": f0ce,
    "border-right": f0cf,
    "border-right-variant": f8a4,
    "border-style": f0d0,
    "border-top": f0d1,
    "border-top-variant": f8a5,
    "border-vertical": f0d2,
    "bottle-wine": f853,
    "bow-tie": f677,
    "bowl": f617,
    "bowling": f0d3,
    "box": f0d4,
    "box-cutter": f0d5,
    "box-shadow": f637,
    "boxing-glove": fb41,
    "braille": f9cf,
    "brain": f9d0,
    "bread-slice": fcca,
    "bread-slice-outline": fccb,
    "bridge": f618,
    "briefcase": f0d6,
    "briefcase-account": fccc,
    "briefcase-account-outline": fccd,
    "briefcase-check": f0d7,
    "briefcase-download": f0d8,
    "briefcase-download-outline": fc19,
    "briefcase-edit": fa97,
    "briefcase-edit-outline": fc1a,
    "briefcase-minus": fa29,
    "briefcase-minus-outline": fc1b,
    "briefcase-outline": f813,
    "briefcase-plus": fa2a,
    "briefcase-plus-outline": fc1c,
    "briefcase-remove": fa2b,
    "briefcase-remove-outline": fc1d,
    "briefcase-search": fa2c,
    "briefcase-search-outline": fc1e,
    "briefcase-upload": f0d9,
    "briefcase-upload-outline": fc1f,
    "brightness-1": f0da,
    "brightness-2": f0db,
    "brightness-3": f0dc,
    "brightness-4": f0dd,
    "brightness-5": f0de,
    "brightness-6": f0df,
    "brightness-7": f0e0,
    "brightness-auto": f0e1,
    "brightness-percent": fcce,
    "broom": f0e2,
    "brush": f0e3,
    "buddhism": f94a,
    "buffer": f619,
    "bug": f0e4,
    "bug-check": fa2d,
    "bug-check-outline": fa2e,
    "bug-outline": fa2f,
    "bugle": fd90,
    "bulldozer": fb07,
    "bullet": fccf,
    "bulletin-board": f0e5,
    "bullhorn": f0e6,
    "bullhorn-outline": fb08,
    "bullseye": f5dd,
    "bullseye-arrow": f8c8,
    "bus": f0e7,
    "bus-alert": fa98,
    "bus-articulated-end": f79b,
    "bus-articulated-front": f79c,
    "bus-clock": f8c9,
    "bus-double-decker": f79d,
    "bus-school": f79e,
    "bus-side": f79f,
    "cached": f0e8,
    "cactus": fd91,
    "cake": f0e9,
    "cake-layered": f0ea,
    "cake-variant": f0eb,
    "calculator": f0ec,
    "calculator-variant": fa99,
    "calendar": f0ed,
    "calendar-alert": fa30,
    "calendar-blank": f0ee,
    "calendar-blank-outline": fb42,
    "calendar-check": f0ef,
    "calendar-check-outline": fc20,
    "calendar-clock": f0f0,
    "calendar-edit": f8a6,
    "calendar-export": fb09,
    "calendar-heart": f9d1,
    "calendar-import": fb0a,
    "calendar-minus": fd38,
    "calendar-month": fdfa,
    "calendar-month-outline": fdfb,
    "calendar-multiple": f0f1,
    "calendar-multiple-check": f0f2,
    "calendar-multiselect": fa31,
    "calendar-outline": fb43,
    "calendar-plus": f0f3,
    "calendar-question": f691,
    "calendar-range": f678,
    "calendar-range-outline": fb44,
    "calendar-remove": f0f4,
    "calendar-remove-outline": fc21,
    "calendar-search": f94b,
    "calendar-star": f9d2,
    "calendar-text": f0f5,
    "calendar-text-outline": fc22,
    "calendar-today": f0f6,
    "calendar-week": fa32,
    "calendar-week-begin": fa33,
    "call-made": f0f7,
    "call-merge": f0f8,
    "call-missed": f0f9,
    "call-received": f0fa,
    "call-split": f0fb,
    "camcorder": f0fc,
    "camcorder-box": f0fd,
    "camcorder-box-off": f0fe,
    "camcorder-off": f0ff,
    "camera": f100,
    "camera-account": f8ca,
    "camera-burst": f692,
    "camera-control": fb45,
    "camera-enhance": f101,
    "camera-enhance-outline": fb46,
    "camera-front": f102,
    "camera-front-variant": f103,
    "camera-gopro": f7a0,
    "camera-image": f8cb,
    "camera-iris": f104,
    "camera-metering-center": f7a1,
    "camera-metering-matrix": f7a2,
    "camera-metering-partial": f7a3,
    "camera-metering-spot": f7a4,
    "camera-off": f5df,
    "camera-outline": fd39,
    "camera-party-mode": f105,
    "camera-rear": f106,
    "camera-rear-variant": f107,
    "camera-retake": fdfc,
    "camera-retake-outline": fdfd,
    "camera-switch": f108,
    "camera-timer": f109,
    "camera-wireless": fd92,
    "camera-wireless-outline": fd93,
    "cancel": f739,
    "candle": f5e2,
    "candycane": f10a,
    "cannabis": f7a5,
    "caps-lock": fa9a,
    "car": f10b,
    "car-back": fdfe,
    "car-battery": f10c,
    "car-brake-abs": fc23,
    "car-brake-alert": fc24,
    "car-brake-hold": fd3a,
    "car-brake-parking": fd3b,
    "car-connected": f10d,
    "car-convertible": f7a6,
    "car-cruise-control": fd3c,
    "car-defrost-front": fd3d,
    "car-defrost-rear": fd3e,
    "car-door": fb47,
    "car-electric": fb48,
    "car-esp": fc25,
    "car-estate": f7a7,
    "car-hatchback": f7a8,
    "car-key": fb49,
    "car-light-dimmed": fc26,
    "car-light-fog": fc27,
    "car-light-high": fc28,
    "car-limousine": f8cc,
    "car-multiple": fb4a,
    "car-off": fdff,
    "car-parking-lights": fd3f,
    "car-pickup": f7a9,
    "car-side": f7aa,
    "car-sports": f7ab,
    "car-tire-alert": fc29,
    "car-traction-control": fd40,
    "car-wash": f10e,
    "caravan": f7ac,
    "card": fb4b,
    "card-bulleted": fb4c,
    "card-bulleted-off": fb4d,
    "card-bulleted-off-outline": fb4e,
    "card-bulleted-outline": fb4f,
    "card-bulleted-settings": fb50,
    "card-bulleted-settings-outline": fb51,
    "card-outline": fb52,
    "card-text": fb53,
    "card-text-outline": fb54,
    "cards": f638,
    "cards-club": f8cd,
    "cards-diamond": f8ce,
    "cards-heart": f8cf,
    "cards-outline": f639,
    "cards-playing-outline": f63a,
    "cards-spade": f8d0,
    "cards-variant": f6c6,
    "carrot": f10f,
    "carry-on-bag-check": fd41,
    "cart": f110,
    "cart-arrow-down": fd42,
    "cart-arrow-right": fc2a,
    "cart-arrow-up": fd43,
    "cart-minus": fd44,
    "cart-off": f66b,
    "cart-outline": f111,
    "cart-plus": f112,
    "cart-remove": fd45,
    "case-sensitive-alt": f113,
    "cash": f114,
    "cash-100": f115,
    "cash-marker": fd94,
    "cash-multiple": f116,
    "cash-refund": fa9b,
    "cash-register": fcd0,
    "cash-usd": f117,
    "cassette": f9d3,
    "cast": f118,
    "cast-connected": f119,
    "cast-education": fe6d,
    "cast-off": f789,
    "castle": f11a,
    "cat": f11b,
    "cctv": f7ad,
    "ceiling-light": f768,
    "cellphone": f11c,
    "cellphone-android": f11d,
    "cellphone-arrow-down": f9d4,
    "cellphone-basic": f11e,
    "cellphone-dock": f11f,
    "cellphone-erase": f94c,
    "cellphone-iphone": f120,
    "cellphone-key": f94d,
    "cellphone-link": f121,
    "cellphone-link-off": f122,
    "cellphone-lock": f94e,
    "cellphone-message": f8d2,
    "cellphone-off": f94f,
    "cellphone-screenshot": fa34,
    "cellphone-settings": f123,
    "cellphone-settings-variant": f950,
    "cellphone-sound": f951,
    "cellphone-text": f8d1,
    "cellphone-wireless": f814,
    "celtic-cross": fcd1,
    "certificate": f124,
    "chair-school": f125,
    "charity": fc2b,
    "chart-arc": f126,
    "chart-areaspline": f127,
    "chart-bar": f128,
    "chart-bar-stacked": f769,
    "chart-bell-curve": fc2c,
    "chart-bubble": f5e3,
    "chart-donut": f7ae,
    "chart-donut-variant": f7af,
    "chart-gantt": f66c,
    "chart-histogram": f129,
    "chart-line": f12a,
    "chart-line-stacked": f76a,
    "chart-line-variant": f7b0,
    "chart-multiline": f8d3,
    "chart-pie": f12b,
    "chart-scatterplot-hexbin": f66d,
    "chart-timeline": f66e,
    "chat": fb55,
    "chat-alert": fb56,
    "chat-processing": fb57,
    "check": f12c,
    "check-all": f12d,
    "check-bold": fe6e,
    "check-box-multiple-outline": fc2d,
    "check-box-outline": fc2e,
    "check-circle": f5e0,
    "check-circle-outline": f5e1,
    "check-decagram": f790,
    "check-network": fc2f,
    "check-network-outline": fc30,
    "check-outline": f854,
    "check-underline": fe70,
    "check-underline-circle": fe71,
    "check-underline-circle-outline": fe72,
    "checkbook": fa9c,
    "checkbox-blank": f12e,
    "checkbox-blank-circle": f12f,
    "checkbox-blank-circle-outline": f130,
    "checkbox-blank-outline": f131,
    "checkbox-intermediate": f855,
    "checkbox-marked": f132,
    "checkbox-marked-circle": f133,
    "checkbox-marked-circle-outline": f134,
    "checkbox-marked-outline": f135,
    "checkbox-multiple-blank": f136,
    "checkbox-multiple-blank-circle": f63b,
    "checkbox-multiple-blank-circle-outline": f63c,
    "checkbox-multiple-blank-outline": f137,
    "checkbox-multiple-marked": f138,
    "checkbox-multiple-marked-circle": f63d,
    "checkbox-multiple-marked-circle-outline": f63e,
    "checkbox-multiple-marked-outline": f139,
    "checkerboard": f13a,
    "chef-hat": fb58,
    "chemical-weapon": f13b,
    "chess-bishop": f85b,
    "chess-king": f856,
    "chess-knight": f857,
    "chess-pawn": f858,
    "chess-queen": f859,
    "chess-rook": f85a,
    "chevron-double-down": f13c,
    "chevron-double-left": f13d,
    "chevron-double-right": f13e,
    "chevron-double-up": f13f,
    "chevron-down": f140,
    "chevron-down-box": f9d5,
    "chevron-down-box-outline": f9d6,
    "chevron-down-circle": fb0b,
    "chevron-down-circle-outline": fb0c,
    "chevron-left": f141,
    "chevron-left-box": f9d7,
    "chevron-left-box-outline": f9d8,
    "chevron-left-circle": fb0d,
    "chevron-left-circle-outline": fb0e,
    "chevron-right": f142,
    "chevron-right-box": f9d9,
    "chevron-right-box-outline": f9da,
    "chevron-right-circle": fb0f,
    "chevron-right-circle-outline": fb10,
    "chevron-triple-down": fd95,
    "chevron-triple-left": fd96,
    "chevron-triple-right": fd97,
    "chevron-triple-up": fd98,
    "chevron-up": f143,
    "chevron-up-box": f9db,
    "chevron-up-box-outline": f9dc,
    "chevron-up-circle": fb11,
    "chevron-up-circle-outline": fb12,
    "chili-hot": f7b1,
    "chili-medium": f7b2,
    "chili-mild": f7b3,
    "chip": f61a,
    "christianity": f952,
    "christianity-outline": fcd2,
    "church": f144,
    "circle": f764,
    "circle-edit-outline": f8d4,
    "circle-medium": f9dd,
    "circle-outline": f765,
    "circle-slice-1": fa9d,
    "circle-slice-2": fa9e,
    "circle-slice-3": fa9f,
    "circle-slice-4": faa0,
    "circle-slice-5": faa1,
    "circle-slice-6": faa2,
    "circle-slice-7": faa3,
    "circle-slice-8": faa4,
    "circle-small": f9de,
    "circular-saw": fe73,
    "cisco-webex": f145,
    "city": f146,
    "city-variant": fa35,
    "city-variant-outline": fa36,
    "clipboard": f147,
    "clipboard-account": f148,
    "clipboard-account-outline": fc31,
    "clipboard-alert": f149,
    "clipboard-alert-outline": fcd3,
    "clipboard-arrow-down": f14a,
    "clipboard-arrow-down-outline": fc32,
    "clipboard-arrow-left": f14b,
    "clipboard-arrow-left-outline": fcd4,
    "clipboard-arrow-right": fcd5,
    "clipboard-arrow-right-outline": fcd6,
    "clipboard-arrow-up": fc33,
    "clipboard-arrow-up-outline": fc34,
    "clipboard-check": f14c,
    "clipboard-check-outline": f8a7,
    "clipboard-flow": f6c7,
    "clipboard-outline": f14d,
    "clipboard-play": fc35,
    "clipboard-play-outline": fc36,
    "clipboard-plus": f750,
    "clipboard-pulse": f85c,
    "clipboard-pulse-outline": f85d,
    "clipboard-text": f14e,
    "clipboard-text-outline": fa37,
    "clipboard-text-play": fc37,
    "clipboard-text-play-outline": fc38,
    "clippy": f14f,
    "clock": f953,
    "clock-alert": f954,
    "clock-alert-outline": f5ce,
    "clock-end": f151,
    "clock-fast": f152,
    "clock-in": f153,
    "clock-out": f154,
    "clock-outline": f150,
    "clock-start": f155,
    "close": f156,
    "close-box": f157,
    "close-box-multiple": fc39,
    "close-box-multiple-outline": fc3a,
    "close-box-outline": f158,
    "close-circle": f159,
    "close-circle-outline": f15a,
    "close-network": f15b,
    "close-network-outline": fc3b,
    "close-octagon": f15c,
    "close-octagon-outline": f15d,
    "close-outline": f6c8,
    "closed-caption": f15e,
    "closed-caption-outline": fd99,
    "cloud": f15f,
    "cloud-alert": f9df,
    "cloud-braces": f7b4,
    "cloud-check": f160,
    "cloud-circle": f161,
    "cloud-download": f162,
    "cloud-download-outline": fb59,
    "cloud-off-outline": f164,
    "cloud-outline": f163,
    "cloud-print": f165,
    "cloud-print-outline": f166,
    "cloud-question": fa38,
    "cloud-search": f955,
    "cloud-search-outline": f956,
    "cloud-sync": f63f,
    "cloud-tags": f7b5,
    "cloud-upload": f167,
    "cloud-upload-outline": fb5a,
    "clover": f815,
    "code-array": f168,
    "code-braces": f169,
    "code-brackets": f16a,
    "code-equal": f16b,
    "code-greater-than": f16c,
    "code-greater-than-or-equal": f16d,
    "code-less-than": f16e,
    "code-less-than-or-equal": f16f,
    "code-not-equal": f170,
    "code-not-equal-variant": f171,
    "code-parentheses": f172,
    "code-string": f173,
    "code-tags": f174,
    "code-tags-check": f693,
    "codepen": f175,
    "coffee": f176,
    "coffee-outline": f6c9,
    "coffee-to-go": f177,
    "coffin": fb5b,
    "cogs": f8d5,
    "coin": f178,
    "coins": f694,
    "collage": f640,
    "collapse-all": faa5,
    "collapse-all-outline": faa6,
    "color-helper": f179,
    "comma": fe74,
    "comma-box": fe75,
    "comma-box-outline": fe76,
    "comma-circle": fe77,
    "comma-circle-outline": fe78,
    "comment": f17a,
    "comment-account": f17b,
    "comment-account-outline": f17c,
    "comment-alert": f17d,
    "comment-alert-outline": f17e,
    "comment-arrow-left": f9e0,
    "comment-arrow-left-outline": f9e1,
    "comment-arrow-right": f9e2,
    "comment-arrow-right-outline": f9e3,
    "comment-check": f17f,
    "comment-check-outline": f180,
    "comment-eye": fa39,
    "comment-eye-outline": fa3a,
    "comment-multiple": f85e,
    "comment-multiple-outline": f181,
    "comment-outline": f182,
    "comment-plus": f9e4,
    "comment-plus-outline": f183,
    "comment-processing": f184,
    "comment-processing-outline": f185,
    "comment-question": f816,
    "comment-question-outline": f186,
    "comment-remove": f5de,
    "comment-remove-outline": f187,
    "comment-search": fa3b,
    "comment-search-outline": fa3c,
    "comment-text": f188,
    "comment-text-multiple": f85f,
    "comment-text-multiple-outline": f860,
    "comment-text-outline": f189,
    "compare": f18a,
    "compass": f18b,
    "compass-off": fb5c,
    "compass-off-outline": fb5d,
    "compass-outline": f18c,
    "console": f18d,
    "console-line": f7b6,
    "console-network": f8a8,
    "console-network-outline": fc3c,
    "contact-mail": f18e,
    "contactless-payment": fd46,
    "contacts": f6ca,
    "contain": fa3d,
    "contain-end": fa3e,
    "contain-start": fa3f,
    "content-copy": f18f,
    "content-cut": f190,
    "content-duplicate": f191,
    "content-paste": f192,
    "content-save": f193,
    "content-save-all": f194,
    "content-save-edit": fcd7,
    "content-save-edit-outline": fcd8,
    "content-save-move": fe79,
    "content-save-move-outline": fe7a,
    "content-save-outline": f817,
    "content-save-settings": f61b,
    "content-save-settings-outline": fb13,
    "contrast": f195,
    "contrast-box": f196,
    "contrast-circle": f197,
    "controller-classic": fb5e,
    "controller-classic-outline": fb5f,
    "cookie": f198,
    "copyright": f5e6,
    "cordova": f957,
    "corn": f7b7,
    "counter": f199,
    "cow": f19a,
    "crane": f861,
    "creation": f1c9,
    "creative-commons": fd47,
    "credit-card": f19b,
    "credit-card-marker": fd9a,
    "credit-card-multiple": f19c,
    "credit-card-off": f5e4,
    "credit-card-plus": f675,
    "credit-card-refund": faa7,
    "credit-card-scan": f19d,
    "credit-card-settings": f8d6,
    "credit-card-wireless": fd48,
    "cricket": fd49,
    "crop": f19e,
    "crop-free": f19f,
    "crop-landscape": f1a0,
    "crop-portrait": f1a1,
    "crop-rotate": f695,
    "crop-square": f1a2,
    "crosshairs": f1a3,
    "crosshairs-gps": f1a4,
    "crown": f1a5,
    "cryengine": f958,
    "crystal-ball": fb14,
    "cube": f1a6,
    "cube-outline": f1a7,
    "cube-scan": fb60,
    "cube-send": f1a8,
    "cube-unfolded": f1a9,
    "cup": f1aa,
    "cup-off": f5e5,
    "cup-water": f1ab,
    "cupcake": f959,
    "curling": f862,
    "currency-bdt": f863,
    "currency-brl": fb61,
    "currency-btc": f1ac,
    "currency-chf": f7b8,
    "currency-cny": f7b9,
    "currency-eth": f7ba,
    "currency-eur": f1ad,
    "currency-gbp": f1ae,
    "currency-ils": fc3d,
    "currency-inr": f1af,
    "currency-jpy": f7bb,
    "currency-krw": f7bc,
    "currency-kzt": f864,
    "currency-ngn": f1b0,
    "currency-php": f9e5,
    "currency-rub": f1b1,
    "currency-sign": f7bd,
    "currency-try": f1b2,
    "currency-twd": f7be,
    "currency-usd": f1b3,
    "currency-usd-off": f679,
    "current-ac": f95a,
    "current-dc": f95b,
    "cursor-default": f1b4,
    "cursor-default-click": fcd9,
    "cursor-default-click-outline": fcda,
    "cursor-default-outline": f1b5,
    "cursor-move": f1b6,
    "cursor-pointer": f1b7,
    "cursor-text": f5e7,
    "database": f1b8,
    "database-check": faa8,
    "database-edit": fb62,
    "database-export": f95d,
    "database-import": f95c,
    "database-lock": faa9,
    "database-minus": f1b9,
    "database-plus": f1ba,
    "database-refresh": fcdb,
    "database-remove": fcdc,
    "database-search": f865,
    "database-settings": fcdd,
    "death-star": f8d7,
    "death-star-variant": f8d8,
    "deathly-hallows": fb63,
    "debian": f8d9,
    "debug-step-into": f1bb,
    "debug-step-out": f1bc,
    "debug-step-over": f1bd,
    "decagram": f76b,
    "decagram-outline": f76c,
    "decimal-decrease": f1be,
    "decimal-increase": f1bf,
    "delete": f1c0,
    "delete-circle": f682,
    "delete-circle-outline": fb64,
    "delete-empty": f6cb,
    "delete-forever": f5e8,
    "delete-forever-outline": fb65,
    "delete-outline": f9e6,
    "delete-restore": f818,
    "delete-sweep": f5e9,
    "delete-sweep-outline": fc3e,
    "delete-variant": f1c1,
    "delta": f1c2,
    "desk-lamp": f95e,
    "deskphone": f1c3,
    "desktop-classic": f7bf,
    "desktop-mac": f1c4,
    "desktop-mac-dashboard": f9e7,
    "desktop-tower": f1c5,
    "desktop-tower-monitor": faaa,
    "details": f1c6,
    "dev-to": fd4a,
    "developer-board": f696,
    "deviantart": f1c7,
    "dialpad": f61c,
    "diameter": fc3f,
    "diameter-outline": fc40,
    "diameter-variant": fc41,
    "diamond": fb66,
    "diamond-outline": fb67,
    "diamond-stone": f1c8,
    "dice-1": f1ca,
    "dice-2": f1cb,
    "dice-3": f1cc,
    "dice-4": f1cd,
    "dice-5": f1ce,
    "dice-6": f1cf,
    "dice-d10": f76e,
    "dice-d12": f866,
    "dice-d20": f5ea,
    "dice-d4": f5eb,
    "dice-d6": f5ec,
    "dice-d8": f5ed,
    "dice-multiple": f76d,
    "dictionary": f61d,
    "dip-switch": f7c0,
    "directions": f1d0,
    "directions-fork": f641,
    "disc": f5ee,
    "disc-alert": f1d1,
    "disc-player": f95f,
    "discord": f66f,
    "dishwasher": faab,
    "disqus": f1d2,
    "disqus-outline": f1d3,
    "diving-flippers": fd9b,
    "diving-helmet": fd9c,
    "diving-scuba": fd9d,
    "diving-scuba-flag": fd9e,
    "diving-scuba-tank": fd9f,
    "diving-scuba-tank-multiple": fda0,
    "diving-snorkel": fda1,
    "division": f1d4,
    "division-box": f1d5,
    "dlna": fa40,
    "dna": f683,
    "dns": f1d6,
    "dns-outline": fb68,
    "do-not-disturb": f697,
    "do-not-disturb-off": f698,
    "docker": f867,
    "doctor": fa41,
    "dog": fa42,
    "dog-service": faac,
    "dog-side": fa43,
    "dolby": f6b2,
    "domain": f1d7,
    "domain-off": fd4b,
    "donkey": f7c1,
    "door": f819,
    "door-closed": f81a,
    "door-open": f81b,
    "doorbell-video": f868,
    "dot-net": faad,
    "dots-horizontal": f1d8,
    "dots-horizontal-circle": f7c2,
    "dots-horizontal-circle-outline": fb69,
    "dots-vertical": f1d9,
    "dots-vertical-circle": f7c3,
    "dots-vertical-circle-outline": fb6a,
    "douban": f699,
    "download": f1da,
    "download-multiple": f9e8,
    "download-network": f6f3,
    "download-network-outline": fc42,
    "download-outline": fb6b,
    "drag": f1db,
    "drag-horizontal": f1dc,
    "drag-variant": fb6c,
    "drag-vertical": f1dd,
    "drama-masks": fcde,
    "drawing": f1de,
    "drawing-box": f1df,
    "dribbble": f1e0,
    "dribbble-box": f1e1,
    "drone": f1e2,
    "dropbox": f1e3,
    "drupal": f1e4,
    "duck": f1e5,
    "dumbbell": f1e6,
    "dump-truck": fc43,
    "ear-hearing": f7c4,
    "ear-hearing-off": fa44,
    "earth": f1e7,
    "earth-box": f6cc,
    "earth-box-off": f6cd,
    "earth-off": f1e8,
    "edge": f1e9,
    "egg": faae,
    "egg-easter": faaf,
    "eight-track": f9e9,
    "eject": f1ea,
    "eject-outline": fb6d,
    "elephant": f7c5,
    "elevation-decline": f1eb,
    "elevation-rise": f1ec,
    "elevator": f1ed,
    "email": f1ee,
    "email-alert": f6ce,
    "email-box": fcdf,
    "email-check": fab0,
    "email-check-outline": fab1,
    "email-lock": f1f1,
    "email-mark-as-unread": fb6e,
    "email-open": f1ef,
    "email-open-outline": f5ef,
    "email-outline": f1f0,
    "email-plus": f9ea,
    "email-plus-outline": f9eb,
    "email-search": f960,
    "email-search-outline": f961,
    "email-variant": f5f0,
    "ember": fb15,
    "emby": f6b3,
    "emoticon": fc44,
    "emoticon-angry": fc45,
    "emoticon-angry-outline": fc46,
    "emoticon-cool": fc47,
    "emoticon-cool-outline": f1f3,
    "emoticon-cry": fc48,
    "emoticon-cry-outline": fc49,
    "emoticon-dead": fc4a,
    "emoticon-dead-outline": f69a,
    "emoticon-devil": fc4b,
    "emoticon-devil-outline": f1f4,
    "emoticon-excited": fc4c,
    "emoticon-excited-outline": f69b,
    "emoticon-happy": fc4d,
    "emoticon-happy-outline": f1f5,
    "emoticon-kiss": fc4e,
    "emoticon-kiss-outline": fc4f,
    "emoticon-neutral": fc50,
    "emoticon-neutral-outline": f1f6,
    "emoticon-outline": f1f2,
    "emoticon-poop": f1f7,
    "emoticon-poop-outline": fc51,
    "emoticon-sad": fc52,
    "emoticon-sad-outline": f1f8,
    "emoticon-tongue": f1f9,
    "emoticon-tongue-outline": fc53,
    "emoticon-wink": fc54,
    "emoticon-wink-outline": fc55,
    "engine": f1fa,
    "engine-off": fa45,
    "engine-off-outline": fa46,
    "engine-outline": f1fb,
    "equal": f1fc,
    "equal-box": f1fd,
    "eraser": f1fe,
    "eraser-variant": f642,
    "escalator": f1ff,
    "eslint": fc56,
    "et": fab2,
    "ethereum": f869,
    "ethernet": f200,
    "ethernet-cable": f201,
    "ethernet-cable-off": f202,
    "etsy": f203,
    "ev-station": f5f1,
    "eventbrite": f7c6,
    "evernote": f204,
    "exclamation": f205,
    "exit-run": fa47,
    "exit-to-app": f206,
    "expand-all": fab3,
    "expand-all-outline": fab4,
    "exponent": f962,
    "exponent-box": f963,
    "export": f207,
    "export-variant": fb6f,
    "eye": f208,
    "eye-check": fce0,
    "eye-check-outline": fce1,
    "eye-circle": fb70,
    "eye-circle-outline": fb71,
    "eye-off": f209,
    "eye-off-outline": f6d0,
    "eye-outline": f6cf,
    "eye-plus": f86a,
    "eye-plus-outline": f86b,
    "eye-settings": f86c,
    "eye-settings-outline": f86d,
    "eyedropper": f20a,
    "eyedropper-variant": f20b,
    "face": f643,
    "face-agent": fd4c,
    "face-outline": fb72,
    "face-profile": f644,
    "face-recognition": fc57,
    "facebook": f20c,
    "facebook-box": f20d,
    "facebook-messenger": f20e,
    "facebook-workplace": fb16,
    "factory": f20f,
    "fan": f210,
    "fan-off": f81c,
    "fast-forward": f211,
    "fast-forward-10": fd4d,
    "fast-forward-30": fce2,
    "fast-forward-outline": f6d1,
    "fax": f212,
    "feather": f6d2,
    "feature-search": fa48,
    "feature-search-outline": fa49,
    "fedora": f8da,
    "ferry": f213,
    "file": f214,
    "file-account": f73a,
    "file-alert": fa4a,
    "file-alert-outline": fa4b,
    "file-cabinet": fab5,
    "file-cancel": fda2,
    "file-cancel-outline": fda3,
    "file-chart": f215,
    "file-check": f216,
    "file-check-outline": fe7b,
    "file-cloud": f217,
    "file-compare": f8a9,
    "file-delimited": f218,
    "file-document": f219,
    "file-document-box": f21a,
    "file-document-box-multiple": fab6,
    "file-document-box-multiple-outline": fab7,
    "file-document-box-outline": f9ec,
    "file-document-edit": fda4,
    "file-document-edit-outline": fda5,
    "file-document-outline": f9ed,
    "file-download": f964,
    "file-download-outline": f965,
    "file-excel": f21b,
    "file-excel-box": f21c,
    "file-export": f21d,
    "file-eye": fda6,
    "file-eye-outline": fda7,
    "file-find": f21e,
    "file-find-outline": fb73,
    "file-hidden": f613,
    "file-image": f21f,
    "file-import": f220,
    "file-lock": f221,
    "file-move": fab8,
    "file-multiple": f222,
    "file-music": f223,
    "file-music-outline": fe7c,
    "file-outline": f224,
    "file-pdf": f225,
    "file-pdf-box": f226,
    "file-pdf-outline": fe7d,
    "file-percent": f81d,
    "file-plus": f751,
    "file-powerpoint": f227,
    "file-powerpoint-box": f228,
    "file-presentation-box": f229,
    "file-question": f86e,
    "file-remove": fb74,
    "file-replace": fb17,
    "file-replace-outline": fb18,
    "file-restore": f670,
    "file-search": fc58,
    "file-search-outline": fc59,
    "file-send": f22a,
    "file-table": fc5a,
    "file-table-outline": fc5b,
    "file-tree": f645,
    "file-undo": f8db,
    "file-upload": fa4c,
    "file-upload-outline": fa4d,
    "file-video": f22b,
    "file-video-outline": fe10,
    "file-word": f22c,
    "file-word-box": f22d,
    "file-xml": f22e,
    "film": f22f,
    "filmstrip": f230,
    "filmstrip-off": f231,
    "filter": f232,
    "filter-outline": f233,
    "filter-remove": f234,
    "filter-remove-outline": f235,
    "filter-variant": f236,
    "finance": f81e,
    "find-replace": f6d3,
    "fingerprint": f237,
    "fire": f238,
    "fire-truck": f8aa,
    "firebase": f966,
    "firefox": f239,
    "fireplace": fe11,
    "fireplace-off": fe12,
    "firework": fe13,
    "fish": f23a,
    "flag": f23b,
    "flag-checkered": f23c,
    "flag-minus": fb75,
    "flag-outline": f23d,
    "flag-plus": fb76,
    "flag-remove": fb77,
    "flag-triangle": f23f,
    "flag-variant": f240,
    "flag-variant-outline": f23e,
    "flare": fd4e,
    "flash": f241,
    "flash-auto": f242,
    "flash-circle": f81f,
    "flash-off": f243,
    "flash-outline": f6d4,
    "flash-red-eye": f67a,
    "flashlight": f244,
    "flashlight-off": f245,
    "flask": f093,
    "flask-empty": f094,
    "flask-empty-outline": f095,
    "flask-outline": f096,
    "flattr": f246,
    "flickr": fce3,
    "flip-to-back": f247,
    "flip-to-front": f248,
    "floor-lamp": f8dc,
    "floor-plan": f820,
    "floppy": f249,
    "floppy-variant": f9ee,
    "flower": f24a,
    "flower-outline": f9ef,
    "flower-poppy": fce4,
    "flower-tulip": f9f0,
    "flower-tulip-outline": f9f1,
    "folder": f24b,
    "folder-account": f24c,
    "folder-account-outline": fb78,
    "folder-alert": fda8,
    "folder-alert-outline": fda9,
    "folder-clock": fab9,
    "folder-clock-outline": faba,
    "folder-download": f24d,
    "folder-edit": f8dd,
    "folder-edit-outline": fdaa,
    "folder-google-drive": f24e,
    "folder-image": f24f,
    "folder-key": f8ab,
    "folder-key-network": f8ac,
    "folder-key-network-outline": fc5c,
    "folder-lock": f250,
    "folder-lock-open": f251,
    "folder-move": f252,
    "folder-multiple": f253,
    "folder-multiple-image": f254,
    "folder-multiple-outline": f255,
    "folder-network": f86f,
    "folder-network-outline": fc5d,
    "folder-open": f76f,
    "folder-open-outline": fdab,
    "folder-outline": f256,
    "folder-plus": f257,
    "folder-plus-outline": fb79,
    "folder-pound": fce5,
    "folder-pound-outline": fce6,
    "folder-remove": f258,
    "folder-remove-outline": fb7a,
    "folder-search": f967,
    "folder-search-outline": f968,
    "folder-star": f69c,
    "folder-star-outline": fb7b,
    "folder-sync": fce7,
    "folder-sync-outline": fce8,
    "folder-text": fc5e,
    "folder-text-outline": fc5f,
    "folder-upload": f259,
    "font-awesome": f03a,
    "food": f25a,
    "food-apple": f25b,
    "food-apple-outline": fc60,
    "food-croissant": f7c7,
    "food-fork-drink": f5f2,
    "food-off": f5f3,
    "food-variant": f25c,
    "football": f25d,
    "football-australian": f25e,
    "football-helmet": f25f,
    "forklift": f7c8,
    "format-align-bottom": f752,
    "format-align-center": f260,
    "format-align-justify": f261,
    "format-align-left": f262,
    "format-align-middle": f753,
    "format-align-right": f263,
    "format-align-top": f754,
    "format-annotation-minus": fabb,
    "format-annotation-plus": f646,
    "format-bold": f264,
    "format-clear": f265,
    "format-color-fill": f266,
    "format-color-highlight": fe14,
    "format-color-text": f69d,
    "format-columns": f8de,
    "format-float-center": f267,
    "format-float-left": f268,
    "format-float-none": f269,
    "format-float-right": f26a,
    "format-font": f6d5,
    "format-font-size-decrease": f9f2,
    "format-font-size-increase": f9f3,
    "format-header-1": f26b,
    "format-header-2": f26c,
    "format-header-3": f26d,
    "format-header-4": f26e,
    "format-header-5": f26f,
    "format-header-6": f270,
    "format-header-decrease": f271,
    "format-header-equal": f272,
    "format-header-increase": f273,
    "format-header-pound": f274,
    "format-horizontal-align-center": f61e,
    "format-horizontal-align-left": f61f,
    "format-horizontal-align-right": f620,
    "format-indent-decrease": f275,
    "format-indent-increase": f276,
    "format-italic": f277,
    "format-letter-case": fb19,
    "format-letter-case-lower": fb1a,
    "format-letter-case-upper": fb1b,
    "format-line-spacing": f278,
    "format-line-style": f5c8,
    "format-line-weight": f5c9,
    "format-list-bulleted": f279,
    "format-list-bulleted-square": fdac,
    "format-list-bulleted-type": f27a,
    "format-list-checkbox": f969,
    "format-list-checks": f755,
    "format-list-numbered": f27b,
    "format-list-numbered-rtl": fce9,
    "format-page-break": f6d6,
    "format-paint": f27c,
    "format-paragraph": f27d,
    "format-pilcrow": f6d7,
    "format-quote-close": f27e,
    "format-quote-open": f756,
    "format-rotate-90": f6a9,
    "format-section": f69e,
    "format-size": f27f,
    "format-strikethrough": f280,
    "format-strikethrough-variant": f281,
    "format-subscript": f282,
    "format-superscript": f283,
    "format-text": f284,
    "format-text-rotation-down": fd4f,
    "format-text-rotation-none": fd50,
    "format-text-variant": fe15,
    "format-text-wrapping-clip": fcea,
    "format-text-wrapping-overflow": fceb,
    "format-text-wrapping-wrap": fcec,
    "format-textbox": fced,
    "format-textdirection-l-to-r": f285,
    "format-textdirection-r-to-l": f286,
    "format-title": f5f4,
    "format-underline": f287,
    "format-vertical-align-bottom": f621,
    "format-vertical-align-center": f622,
    "format-vertical-align-top": f623,
    "format-wrap-inline": f288,
    "format-wrap-square": f289,
    "format-wrap-tight": f28a,
    "format-wrap-top-bottom": f28b,
    "forum": f28c,
    "forum-outline": f821,
    "forward": f28d,
    "forwardburger": fd51,
    "fountain": f96a,
    "fountain-pen": fcee,
    "fountain-pen-tip": fcef,
    "foursquare": f28e,
    "freebsd": f8df,
    "fridge": f290,
    "fridge-bottom": f292,
    "fridge-outline": f28f,
    "fridge-top": f291,
    "fuel": f7c9,
    "fullscreen": f293,
    "fullscreen-exit": f294,
    "function": f295,
    "function-variant": f870,
    "fuse": fc61,
    "fuse-blade": fc62,
    "gamepad": f296,
    "gamepad-circle": fe16,
    "gamepad-circle-down": fe17,
    "gamepad-circle-left": fe18,
    "gamepad-circle-outline": fe19,
    "gamepad-circle-right": fe1a,
    "gamepad-circle-up": fe1b,
    "gamepad-down": fe1c,
    "gamepad-left": fe1d,
    "gamepad-right": fe1e,
    "gamepad-round": fe1f,
    "gamepad-round-down": fe20,
    "gamepad-round-left": fe21,
    "gamepad-round-outline": fe22,
    "gamepad-round-right": fe23,
    "gamepad-round-up": fe24,
    "gamepad-up": fe25,
    "gamepad-variant": f297,
    "gantry-crane": fdad,
    "garage": f6d8,
    "garage-alert": f871,
    "garage-open": f6d9,
    "gas-cylinder": f647,
    "gas-station": f298,
    "gate": f299,
    "gate-and": f8e0,
    "gate-nand": f8e1,
    "gate-nor": f8e2,
    "gate-not": f8e3,
    "gate-or": f8e4,
    "gate-xnor": f8e5,
    "gate-xor": f8e6,
    "gatsby": fe26,
    "gauge": f29a,
    "gauge-empty": f872,
    "gauge-full": f873,
    "gauge-low": f874,
    "gavel": f29b,
    "gender-female": f29c,
    "gender-male": f29d,
    "gender-male-female": f29e,
    "gender-transgender": f29f,
    "gentoo": f8e7,
    "gesture": f7ca,
    "gesture-double-tap": f73b,
    "gesture-pinch": fabc,
    "gesture-spread": fabd,
    "gesture-swipe": fd52,
    "gesture-swipe-down": f73c,
    "gesture-swipe-horizontal": fabe,
    "gesture-swipe-left": f73d,
    "gesture-swipe-right": f73e,
    "gesture-swipe-up": f73f,
    "gesture-swipe-vertical": fabf,
    "gesture-tap": f740,
    "gesture-tap-hold": fd53,
    "gesture-two-double-tap": f741,
    "gesture-two-tap": f742,
    "ghost": f2a0,
    "ghost-off": f9f4,
    "gif": fd54,
    "gift": fe27,
    "gift-outline": f2a1,
    "git": f2a2,
    "github-box": f2a3,
    "github-circle": f2a4,
    "github-face": f6da,
    "gitlab": fb7c,
    "glass-cocktail": f356,
    "glass-flute": f2a5,
    "glass-mug": f2a6,
    "glass-stange": f2a7,
    "glass-tulip": f2a8,
    "glass-wine": f875,
    "glassdoor": f2a9,
    "glasses": f2aa,
    "globe-model": f8e8,
    "gmail": f2ab,
    "gnome": f2ac,
    "go-kart": fd55,
    "go-kart-track": fd56,
    "gog": fb7d,
    "golf": f822,
    "gondola": f685,
    "goodreads": fd57,
    "google": f2ad,
    "google-adwords": fc63,
    "google-allo": f801,
    "google-analytics": f7cb,
    "google-assistant": f7cc,
    "google-cardboard": f2ae,
    "google-chrome": f2af,
    "google-circles": f2b0,
    "google-circles-communities": f2b1,
    "google-circles-extended": f2b2,
    "google-circles-group": f2b3,
    "google-classroom": f2c0,
    "google-controller": f2b4,
    "google-controller-off": f2b5,
    "google-drive": f2b6,
    "google-earth": f2b7,
    "google-fit": f96b,
    "google-glass": f2b8,
    "google-hangouts": f2c9,
    "google-home": f823,
    "google-keep": f6db,
    "google-lens": f9f5,
    "google-maps": f5f5,
    "google-nearby": f2b9,
    "google-pages": f2ba,
    "google-photos": f6dc,
    "google-physical-web": f2bb,
    "google-play": f2bc,
    "google-plus": f2bd,
    "google-plus-box": f2be,
    "google-spreadsheet": f9f6,
    "google-street-view": fc64,
    "google-translate": f2bf,
    "gpu": f8ad,
    "gradient": f69f,
    "grain": fd58,
    "graphql": f876,
    "grave-stone": fb7e,
    "grease-pencil": f648,
    "greater-than": f96c,
    "greater-than-or-equal": f96d,
    "grid": f2c1,
    "grid-large": f757,
    "grid-off": f2c2,
    "grill": fe28,
    "group": f2c3,
    "guitar-acoustic": f770,
    "guitar-electric": f2c4,
    "guitar-pick": f2c5,
    "guitar-pick-outline": f2c6,
    "guy-fawkes-mask": f824,
    "hackernews": f624,
    "hail": fac0,
    "halloween": fb7f,
    "hamburger": f684,
    "hammer": f8e9,
    "hand": fa4e,
    "hand-left": fe29,
    "hand-okay": fa4f,
    "hand-peace": fa50,
    "hand-peace-variant": fa51,
    "hand-pointing-down": fa52,
    "hand-pointing-left": fa53,
    "hand-pointing-right": f2c7,
    "hand-pointing-up": fa54,
    "hand-right": fe2a,
    "hand-saw": fe2b,
    "hanger": f2c8,
    "hard-hat": f96e,
    "harddisk": f2ca,
    "hat-fedora": fb80,
    "hazard-lights": fc65,
    "hdr": fd59,
    "hdr-off": fd5a,
    "headphones": f2cb,
    "headphones-bluetooth": f96f,
    "headphones-box": f2cc,
    "headphones-off": f7cd,
    "headphones-settings": f2cd,
    "headset": f2ce,
    "headset-dock": f2cf,
    "headset-off": f2d0,
    "heart": f2d1,
    "heart-box": f2d2,
    "heart-box-outline": f2d3,
    "heart-broken": f2d4,
    "heart-broken-outline": fcf0,
    "heart-circle": f970,
    "heart-circle-outline": f971,
    "heart-half": f6de,
    "heart-half-full": f6dd,
    "heart-half-outline": f6df,
    "heart-multiple": fa55,
    "heart-multiple-outline": fa56,
    "heart-off": f758,
    "heart-outline": f2d5,
    "heart-pulse": f5f6,
    "helicopter": fac1,
    "help": f2d6,
    "help-box": f78a,
    "help-circle": f2d7,
    "help-circle-outline": f625,
    "help-network": f6f4,
    "help-network-outline": fc66,
    "help-rhombus": fb81,
    "help-rhombus-outline": fb82,
    "hexagon": f2d8,
    "hexagon-multiple": f6e0,
    "hexagon-outline": f2d9,
    "hexagon-slice-1": fac2,
    "hexagon-slice-2": fac3,
    "hexagon-slice-3": fac4,
    "hexagon-slice-4": fac5,
    "hexagon-slice-5": fac6,
    "hexagon-slice-6": fac7,
    "hexagram": fac8,
    "hexagram-outline": fac9,
    "high-definition": f7ce,
    "high-definition-box": f877,
    "highway": f5f7,
    "hiking": fd5b,
    "hinduism": f972,
    "history": f2da,
    "hockey-puck": f878,
    "hockey-sticks": f879,
    "hololens": f2db,
    "home": f2dc,
    "home-account": f825,
    "home-alert": f87a,
    "home-assistant": f7cf,
    "home-automation": f7d0,
    "home-circle": f7d1,
    "home-city": fcf1,
    "home-city-outline": fcf2,
    "home-currency-usd": f8ae,
    "home-floor-0": fdae,
    "home-floor-1": fd5c,
    "home-floor-2": fd5d,
    "home-floor-3": fd5e,
    "home-floor-a": fd5f,
    "home-floor-b": fd60,
    "home-floor-g": fd61,
    "home-floor-l": fd62,
    "home-floor-negative-1": fdaf,
    "home-group": fdb0,
    "home-heart": f826,
    "home-lock": f8ea,
    "home-lock-open": f8eb,
    "home-map-marker": f5f8,
    "home-minus": f973,
    "home-modern": f2dd,
    "home-outline": f6a0,
    "home-plus": f974,
    "home-variant": f2de,
    "home-variant-outline": fb83,
    "hook": f6e1,
    "hook-off": f6e2,
    "hops": f2df,
    "horseshoe": fa57,
    "hospital": f2e0,
    "hospital-building": f2e1,
    "hospital-marker": f2e2,
    "hot-tub": f827,
    "hotel": f2e3,
    "houzz": f2e4,
    "houzz-box": f2e5,
    "hubspot": fcf3,
    "hulu": f828,
    "human": f2e6,
    "human-child": f2e7,
    "human-female": f649,
    "human-female-boy": fa58,
    "human-female-female": fa59,
    "human-female-girl": fa5a,
    "human-greeting": f64a,
    "human-handsdown": f64b,
    "human-handsup": f64c,
    "human-male": f64d,
    "human-male-boy": fa5b,
    "human-male-female": f2e8,
    "human-male-girl": fa5c,
    "human-male-male": fa5d,
    "human-pregnant": f5cf,
    "humble-bundle": f743,
    "ice-cream": f829,
    "iframe": fc67,
    "iframe-outline": fc68,
    "image": f2e9,
    "image-album": f2ea,
    "image-area": f2eb,
    "image-area-close": f2ec,
    "image-broken": f2ed,
    "image-broken-variant": f2ee,
    "image-filter": f2ef,
    "image-filter-black-white": f2f0,
    "image-filter-center-focus": f2f1,
    "image-filter-center-focus-weak": f2f2,
    "image-filter-drama": f2f3,
    "image-filter-frames": f2f4,
    "image-filter-hdr": f2f5,
    "image-filter-none": f2f6,
    "image-filter-tilt-shift": f2f7,
    "image-filter-vintage": f2f8,
    "image-frame": fe2c,
    "image-move": f9f7,
    "image-multiple": f2f9,
    "image-off": f82a,
    "image-outline": f975,
    "image-plus": f87b,
    "image-search": f976,
    "image-search-outline": f977,
    "image-size-select-actual": fc69,
    "image-size-select-large": fc6a,
    "image-size-select-small": fc6b,
    "import": f2fa,
    "inbox": f686,
    "inbox-arrow-down": f2fb,
    "inbox-arrow-up": f3d1,
    "inbox-multiple": f8af,
    "inbox-multiple-outline": fb84,
    "incognito": f5f9,
    "infinity": f6e3,
    "information": f2fc,
    "information-outline": f2fd,
    "information-variant": f64e,
    "instagram": f2fe,
    "instapaper": f2ff,
    "internet-explorer": f300,
    "invert-colors": f301,
    "invert-colors-off": fe2d,
    "ip": fa5e,
    "ip-network": fa5f,
    "ip-network-outline": fc6c,
    "ipod": fc6d,
    "islam": f978,
    "itunes": f676,
    "jabber": fdb1,
    "jeepney": f302,
    "jira": f303,
    "jquery": f87c,
    "jsfiddle": f304,
    "json": f626,
    "judaism": f979,
    "kabaddi": fd63,
    "karate": f82b,
    "keg": f305,
    "kettle": f5fa,
    "key": f306,
    "key-change": f307,
    "key-minus": f308,
    "key-outline": fdb2,
    "key-plus": f309,
    "key-remove": f30a,
    "key-variant": f30b,
    "keyboard": f30c,
    "keyboard-backspace": f30d,
    "keyboard-caps": f30e,
    "keyboard-close": f30f,
    "keyboard-off": f310,
    "keyboard-off-outline": fe2e,
    "keyboard-outline": f97a,
    "keyboard-return": f311,
    "keyboard-settings": f9f8,
    "keyboard-settings-outline": f9f9,
    "keyboard-tab": f312,
    "keyboard-variant": f313,
    "kickstarter": f744,
    "knife": f9fa,
    "knife-military": f9fb,
    "kodi": f314,
    "label": f315,
    "label-off": faca,
    "label-off-outline": facb,
    "label-outline": f316,
    "label-variant": facc,
    "label-variant-outline": facd,
    "ladybug": f82c,
    "lambda": f627,
    "lamp": f6b4,
    "lan": f317,
    "lan-connect": f318,
    "lan-disconnect": f319,
    "lan-pending": f31a,
    "language-c": f671,
    "language-cpp": f672,
    "language-csharp": f31b,
    "language-css3": f31c,
    "language-go": f7d2,
    "language-haskell": fc6e,
    "language-html5": f31d,
    "language-java": fb1c,
    "language-javascript": f31e,
    "language-lua": f8b0,
    "language-php": f31f,
    "language-python": f320,
    "language-python-text": f321,
    "language-r": f7d3,
    "language-ruby-on-rails": face,
    "language-swift": f6e4,
    "language-typescript": f6e5,
    "laptop": f322,
    "laptop-chromebook": f323,
    "laptop-mac": f324,
    "laptop-off": f6e6,
    "laptop-windows": f325,
    "laravel": facf,
    "lastfm": f326,
    "lastpass": f446,
    "launch": f327,
    "lava-lamp": f7d4,
    "layers": f328,
    "layers-minus": fe2f,
    "layers-off": f329,
    "layers-off-outline": f9fc,
    "layers-outline": f9fd,
    "layers-plus": fe30,
    "layers-remove": fe31,
    "lead-pencil": f64f,
    "leaf": f32a,
    "leaf-maple": fc6f,
    "leak": fdb3,
    "leak-off": fdb4,
    "led-off": f32b,
    "led-on": f32c,
    "led-outline": f32d,
    "led-strip": f7d5,
    "led-variant-off": f32e,
    "led-variant-on": f32f,
    "led-variant-outline": f330,
    "less-than": f97b,
    "less-than-or-equal": f97c,
    "library": f331,
    "library-books": f332,
    "library-movie": fcf4,
    "library-music": f333,
    "library-plus": f334,
    "library-shelves": fb85,
    "library-video": fcf5,
    "lifebuoy": f87d,
    "light-switch": f97d,
    "lightbulb": f335,
    "lightbulb-off": fe32,
    "lightbulb-off-outline": fe33,
    "lightbulb-on": f6e7,
    "lightbulb-on-outline": f6e8,
    "lightbulb-outline": f336,
    "lighthouse": f9fe,
    "lighthouse-on": f9ff,
    "link": f337,
    "link-box": fcf6,
    "link-box-outline": fcf7,
    "link-box-variant": fcf8,
    "link-box-variant-outline": fcf9,
    "link-off": f338,
    "link-plus": fc70,
    "link-variant": f339,
    "link-variant-off": f33a,
    "linkedin": f33b,
    "linkedin-box": f33c,
    "linux": f33d,
    "linux-mint": f8ec,
    "litecoin": fa60,
    "loading": f771,
    "lock": f33e,
    "lock-alert": f8ed,
    "lock-clock": f97e,
    "lock-open": f33f,
    "lock-open-outline": f340,
    "lock-outline": f341,
    "lock-pattern": f6e9,
    "lock-plus": f5fb,
    "lock-question": f8ee,
    "lock-reset": f772,
    "lock-smart": f8b1,
    "locker": f7d6,
    "locker-multiple": f7d7,
    "login": f342,
    "login-variant": f5fc,
    "logout": f343,
    "logout-variant": f5fd,
    "looks": f344,
    "loop": f6ea,
    "loupe": f345,
    "lumx": f346,
    "lyft": fb1d,
    "magnet": f347,
    "magnet-on": f348,
    "magnify": f349,
    "magnify-close": f97f,
    "magnify-minus": f34a,
    "magnify-minus-cursor": fa61,
    "magnify-minus-outline": f6eb,
    "magnify-plus": f34b,
    "magnify-plus-cursor": fa62,
    "magnify-plus-outline": f6ec,
    "mail-ru": f34c,
    "mailbox": f6ed,
    "mailbox-open": fd64,
    "mailbox-open-outline": fd65,
    "mailbox-open-up": fd66,
    "mailbox-open-up-outline": fd67,
    "mailbox-outline": fd68,
    "mailbox-up": fd69,
    "mailbox-up-outline": fd6a,
    "map": f34d,
    "map-clock": fcfa,
    "map-clock-outline": fcfb,
    "map-legend": fa00,
    "map-marker": f34e,
    "map-marker-check": fc71,
    "map-marker-circle": f34f,
    "map-marker-distance": f8ef,
    "map-marker-minus": f650,
    "map-marker-multiple": f350,
    "map-marker-off": f351,
    "map-marker-outline": f7d8,
    "map-marker-path": fcfc,
    "map-marker-plus": f651,
    "map-marker-radius": f352,
    "map-minus": f980,
    "map-outline": f981,
    "map-plus": f982,
    "map-search": f983,
    "map-search-outline": f984,
    "mapbox": fb86,
    "margin": f353,
    "markdown": f354,
    "marker": f652,
    "marker-cancel": fdb5,
    "marker-check": f355,
    "mastodon": fad0,
    "mastodon-variant": fad1,
    "material-design": f985,
    "material-ui": f357,
    "math-compass": f358,
    "math-cos": fc72,
    "math-sin": fc73,
    "math-tan": fc74,
    "matrix": f628,
    "maxcdn": f359,
    "medal": f986,
    "medical-bag": f6ee,
    "medium": f35a,
    "meetup": fad2,
    "memory": f35b,
    "menu": f35c,
    "menu-down": f35d,
    "menu-down-outline": f6b5,
    "menu-left": f35e,
    "menu-left-outline": fa01,
    "menu-open": fb87,
    "menu-right": f35f,
    "menu-right-outline": fa02,
    "menu-swap": fa63,
    "menu-swap-outline": fa64,
    "menu-up": f360,
    "menu-up-outline": f6b6,
    "message": f361,
    "message-alert": f362,
    "message-alert-outline": fa03,
    "message-bulleted": f6a1,
    "message-bulleted-off": f6a2,
    "message-draw": f363,
    "message-image": f364,
    "message-outline": f365,
    "message-plus": f653,
    "message-processing": f366,
    "message-reply": f367,
    "message-reply-text": f368,
    "message-settings": f6ef,
    "message-settings-variant": f6f0,
    "message-text": f369,
    "message-text-outline": f36a,
    "message-video": f36b,
    "meteor": f629,
    "metronome": f7d9,
    "metronome-tick": f7da,
    "micro-sd": f7db,
    "microphone": f36c,
    "microphone-minus": f8b2,
    "microphone-off": f36d,
    "microphone-outline": f36e,
    "microphone-plus": f8b3,
    "microphone-settings": f36f,
    "microphone-variant": f370,
    "microphone-variant-off": f371,
    "microscope": f654,
    "microsoft": f372,
    "microsoft-dynamics": f987,
    "microwave": fc75,
    "midi": f8f0,
    "midi-port": f8f1,
    "mine": fdb6,
    "minecraft": f373,
    "mini-sd": fa04,
    "minidisc": fa05,
    "minus": f374,
    "minus-box": f375,
    "minus-box-outline": f6f1,
    "minus-circle": f376,
    "minus-circle-outline": f377,
    "minus-network": f378,
    "minus-network-outline": fc76,
    "mixcloud": f62a,
    "mixed-martial-arts": fd6b,
    "mixed-reality": f87e,
    "mixer": f7dc,
    "molecule": fb88,
    "monitor": f379,
    "monitor-cellphone": f988,
    "monitor-cellphone-star": f989,
    "monitor-dashboard": fa06,
    "monitor-lock": fdb7,
    "monitor-multiple": f37a,
    "monitor-off": fd6c,
    "monitor-screenshot": fe34,
    "monitor-star": fdb8,
    "more": f37b,
    "mother-nurse": fcfd,
    "motion-sensor": fd6d,
    "motorbike": f37c,
    "mouse": f37d,
    "mouse-bluetooth": f98a,
    "mouse-off": f37e,
    "mouse-variant": f37f,
    "mouse-variant-off": f380,
    "move-resize": f655,
    "move-resize-variant": f656,
    "movie": f381,
    "movie-outline": fdb9,
    "movie-roll": f7dd,
    "muffin": f98b,
    "multiplication": f382,
    "multiplication-box": f383,
    "mushroom": f7de,
    "mushroom-outline": f7df,
    "music": f759,
    "music-box": f384,
    "music-box-outline": f385,
    "music-circle": f386,
    "music-circle-outline": fad3,
    "music-note": f387,
    "music-note-bluetooth": f5fe,
    "music-note-bluetooth-off": f5ff,
    "music-note-eighth": f388,
    "music-note-half": f389,
    "music-note-off": f38a,
    "music-note-plus": fdba,
    "music-note-quarter": f38b,
    "music-note-sixteenth": f38c,
    "music-note-whole": f38d,
    "music-off": f75a,
    "nail": fdbb,
    "nas": f8f2,
    "nativescript": f87f,
    "nature": f38e,
    "nature-people": f38f,
    "navigation": f390,
    "near-me": f5cd,
    "needle": f391,
    "netflix": f745,
    "network": f6f2,
    "network-off": fc77,
    "network-off-outline": fc78,
    "network-outline": fc79,
    "network-strength-1": f8f3,
    "network-strength-1-alert": f8f4,
    "network-strength-2": f8f5,
    "network-strength-2-alert": f8f6,
    "network-strength-3": f8f7,
    "network-strength-3-alert": f8f8,
    "network-strength-4": f8f9,
    "network-strength-4-alert": f8fa,
    "network-strength-off": f8fb,
    "network-strength-off-outline": f8fc,
    "network-strength-outline": f8fd,
    "new-box": f394,
    "newspaper": f395,
    "nfc": f396,
    "nfc-off": fe35,
    "nfc-search-variant": fe36,
    "nfc-tap": f397,
    "nfc-variant": f398,
    "nfc-variant-off": fe37,
    "ninja": f773,
    "nintendo-switch": f7e0,
    "nodejs": f399,
    "not-equal": f98c,
    "not-equal-variant": f98d,
    "note": f39a,
    "note-multiple": f6b7,
    "note-multiple-outline": f6b8,
    "note-outline": f39b,
    "note-plus": f39c,
    "note-plus-outline": f39d,
    "note-text": f39e,
    "notebook": f82d,
    "notebook-multiple": fe38,
    "notification-clear-all": f39f,
    "npm": f6f6,
    "npm-variant": f98e,
    "npm-variant-outline": f98f,
    "nuke": f6a3,
    "null": f7e1,
    "numeric": f3a0,
    "numeric-0": 0030,
    "numeric-0-box": f3a1,
    "numeric-0-box-multiple-outline": f3a2,
    "numeric-0-box-outline": f3a3,
    "numeric-0-circle": fc7a,
    "numeric-0-circle-outline": fc7b,
    "numeric-1": 0031,
    "numeric-1-box": f3a4,
    "numeric-1-box-multiple-outline": f3a5,
    "numeric-1-box-outline": f3a6,
    "numeric-1-circle": fc7c,
    "numeric-1-circle-outline": fc7d,
    "numeric-2": 0032,
    "numeric-2-box": f3a7,
    "numeric-2-box-multiple-outline": f3a8,
    "numeric-2-box-outline": f3a9,
    "numeric-2-circle": fc7e,
    "numeric-2-circle-outline": fc7f,
    "numeric-3": 0033,
    "numeric-3-box": f3aa,
    "numeric-3-box-multiple-outline": f3ab,
    "numeric-3-box-outline": f3ac,
    "numeric-3-circle": fc80,
    "numeric-3-circle-outline": fc81,
    "numeric-4": 0034,
    "numeric-4-box": f3ad,
    "numeric-4-box-multiple-outline": f3ae,
    "numeric-4-box-outline": f3af,
    "numeric-4-circle": fc82,
    "numeric-4-circle-outline": fc83,
    "numeric-5": 0035,
    "numeric-5-box": f3b0,
    "numeric-5-box-multiple-outline": f3b1,
    "numeric-5-box-outline": f3b2,
    "numeric-5-circle": fc84,
    "numeric-5-circle-outline": fc85,
    "numeric-6": 0036,
    "numeric-6-box": f3b3,
    "numeric-6-box-multiple-outline": f3b4,
    "numeric-6-box-outline": f3b5,
    "numeric-6-circle": fc86,
    "numeric-6-circle-outline": fc87,
    "numeric-7": 0037,
    "numeric-7-box": f3b6,
    "numeric-7-box-multiple-outline": f3b7,
    "numeric-7-box-outline": f3b8,
    "numeric-7-circle": fc88,
    "numeric-7-circle-outline": fc89,
    "numeric-8": 0038,
    "numeric-8-box": f3b9,
    "numeric-8-box-multiple-outline": f3ba,
    "numeric-8-box-outline": f3bb,
    "numeric-8-circle": fc8a,
    "numeric-8-circle-outline": fc8b,
    "numeric-9": 0039,
    "numeric-9-box": f3bc,
    "numeric-9-box-multiple-outline": f3bd,
    "numeric-9-box-outline": f3be,
    "numeric-9-circle": fc8c,
    "numeric-9-circle-outline": fc8d,
    "numeric-9-plus-box": f3bf,
    "numeric-9-plus-box-multiple-outline": f3c0,
    "numeric-9-plus-box-outline": f3c1,
    "numeric-9-plus-circle": fc8e,
    "numeric-9-plus-circle-outline": fc8f,
    "nut": f6f7,
    "nutrition": f3c2,
    "oar": f67b,
    "ocarina": fdbc,
    "octagon": f3c3,
    "octagon-outline": f3c4,
    "octagram": f6f8,
    "octagram-outline": f774,
    "odnoklassniki": f3c5,
    "office": f3c6,
    "office-building": f990,
    "oil": f3c7,
    "oil-temperature": f3c8,
    "omega": f3c9,
    "one-up": fb89,
    "onedrive": f3ca,
    "onenote": f746,
    "onepassword": f880,
    "opacity": f5cc,
    "open-in-app": f3cb,
    "open-in-new": f3cc,
    "open-source-initiative": fb8a,
    "openid": f3cd,
    "opera": f3ce,
    "orbit": f018,
    "origin": fb2b,
    "ornament": f3cf,
    "ornament-variant": f3d0,
    "outlook": fcfe,
    "owl": f3d2,
    "pac-man": fb8b,
    "package": f3d3,
    "package-down": f3d4,
    "package-up": f3d5,
    "package-variant": f3d6,
    "package-variant-closed": f3d7,
    "page-first": f600,
    "page-last": f601,
    "page-layout-body": f6f9,
    "page-layout-footer": f6fa,
    "page-layout-header": f6fb,
    "page-layout-sidebar-left": f6fc,
    "page-layout-sidebar-right": f6fd,
    "page-next": fb8c,
    "page-next-outline": fb8d,
    "page-previous": fb8e,
    "page-previous-outline": fb8f,
    "palette": f3d8,
    "palette-advanced": f3d9,
    "palette-outline": fe6c,
    "palette-swatch": f8b4,
    "pan": fb90,
    "pan-bottom-left": fb91,
    "pan-bottom-right": fb92,
    "pan-down": fb93,
    "pan-horizontal": fb94,
    "pan-left": fb95,
    "pan-right": fb96,
    "pan-top-left": fb97,
    "pan-top-right": fb98,
    "pan-up": fb99,
    "pan-vertical": fb9a,
    "panda": f3da,
    "pandora": f3db,
    "panorama": f3dc,
    "panorama-fisheye": f3dd,
    "panorama-horizontal": f3de,
    "panorama-vertical": f3df,
    "panorama-wide-angle": f3e0,
    "paper-cut-vertical": f3e1,
    "paperclip": f3e2,
    "parachute": fc90,
    "parachute-outline": fc91,
    "parking": f3e3,
    "passport": f7e2,
    "passport-biometric": fdbd,
    "patreon": f881,
    "pause": f3e4,
    "pause-circle": f3e5,
    "pause-circle-outline": f3e6,
    "pause-octagon": f3e7,
    "pause-octagon-outline": f3e8,
    "paw": f3e9,
    "paw-off": f657,
    "paypal": f882,
    "pdf-box": fe39,
    "peace": f883,
    "pen": f3ea,
    "pen-lock": fdbe,
    "pen-minus": fdbf,
    "pen-off": fdc0,
    "pen-plus": fdc1,
    "pen-remove": fdc2,
    "pencil": f3eb,
    "pencil-box": f3ec,
    "pencil-box-outline": f3ed,
    "pencil-circle": f6fe,
    "pencil-circle-outline": f775,
    "pencil-lock": f3ee,
    "pencil-lock-outline": fdc3,
    "pencil-minus": fdc4,
    "pencil-minus-outline": fdc5,
    "pencil-off": f3ef,
    "pencil-off-outline": fdc6,
    "pencil-outline": fc92,
    "pencil-plus": fdc7,
    "pencil-plus-outline": fdc8,
    "pencil-remove": fdc9,
    "pencil-remove-outline": fdca,
    "pentagon": f6ff,
    "pentagon-outline": f700,
    "percent": f3f0,
    "periodic-table": f8b5,
    "periodic-table-co2": f7e3,
    "periscope": f747,
    "perspective-less": fcff,
    "perspective-more": fd00,
    "pharmacy": f3f1,
    "phone": f3f2,
    "phone-bluetooth": f3f3,
    "phone-classic": f602,
    "phone-forward": f3f4,
    "phone-hangup": f3f5,
    "phone-in-talk": f3f6,
    "phone-incoming": f3f7,
    "phone-lock": f3f8,
    "phone-log": f3f9,
    "phone-minus": f658,
    "phone-missed": f3fa,
    "phone-off": fdcb,
    "phone-outgoing": f3fb,
    "phone-outline": fdcc,
    "phone-paused": f3fc,
    "phone-plus": f659,
    "phone-return": f82e,
    "phone-rotate-landscape": f884,
    "phone-rotate-portrait": f885,
    "phone-settings": f3fd,
    "phone-voip": f3fe,
    "pi": f3ff,
    "pi-box": f400,
    "pi-hole": fdcd,
    "piano": f67c,
    "pickaxe": f8b6,
    "picture-in-picture-bottom-right": fe3a,
    "picture-in-picture-bottom-right-outline": fe3b,
    "picture-in-picture-top-right": fe3c,
    "picture-in-picture-top-right-outline": fe3d,
    "pier": f886,
    "pier-crane": f887,
    "pig": f401,
    "pill": f402,
    "pillar": f701,
    "pin": f403,
    "pin-off": f404,
    "pin-off-outline": f92f,
    "pin-outline": f930,
    "pine-tree": f405,
    "pine-tree-box": f406,
    "pinterest": f407,
    "pinterest-box": f408,
    "pinwheel": fad4,
    "pinwheel-outline": fad5,
    "pipe": f7e4,
    "pipe-disconnected": f7e5,
    "pipe-leak": f888,
    "pirate": fa07,
    "pistol": f702,
    "piston": f889,
    "pizza": f409,
    "play": f40a,
    "play-box-outline": f40b,
    "play-circle": f40c,
    "play-circle-outline": f40d,
    "play-network": f88a,
    "play-network-outline": fc93,
    "play-pause": f40e,
    "play-protected-content": f40f,
    "play-speed": f8fe,
    "playlist-check": f5c7,
    "playlist-edit": f8ff,
    "playlist-minus": f410,
    "playlist-music": fc94,
    "playlist-music-outline": fc95,
    "playlist-play": f411,
    "playlist-plus": f412,
    "playlist-remove": f413,
    "playlist-star": fdce,
    "playstation": f414,
    "plex": f6b9,
    "plus": f415,
    "plus-box": f416,
    "plus-box-outline": f703,
    "plus-circle": f417,
    "plus-circle-multiple-outline": f418,
    "plus-circle-outline": f419,
    "plus-minus": f991,
    "plus-minus-box": f992,
    "plus-network": f41a,
    "plus-network-outline": fc96,
    "plus-one": f41b,
    "plus-outline": f704,
    "pocket": f41c,
    "podcast": f993,
    "podium": fd01,
    "podium-bronze": fd02,
    "podium-gold": fd03,
    "podium-silver": fd04,
    "point-of-sale": fd6e,
    "pokeball": f41d,
    "pokemon-go": fa08,
    "poker-chip": f82f,
    "polaroid": f41e,
    "poll": f41f,
    "poll-box": f420,
    "polymer": f421,
    "pool": f606,
    "popcorn": f422,
    "postage-stamp": fc97,
    "pot": f65a,
    "pot-mix": f65b,
    "pound": f423,
    "pound-box": f424,
    "power": f425,
    "power-cycle": f900,
    "power-off": f901,
    "power-on": f902,
    "power-plug": f6a4,
    "power-plug-off": f6a5,
    "power-settings": f426,
    "power-sleep": f903,
    "power-socket": f427,
    "power-socket-au": f904,
    "power-socket-eu": f7e6,
    "power-socket-uk": f7e7,
    "power-socket-us": f7e8,
    "power-standby": f905,
    "powershell": fa09,
    "prescription": f705,
    "presentation": f428,
    "presentation-play": f429,
    "printer": f42a,
    "printer-3d": f42b,
    "printer-3d-nozzle": fe3e,
    "printer-3d-nozzle-outline": fe3f,
    "printer-alert": f42c,
    "printer-off": fe40,
    "printer-settings": f706,
    "printer-wireless": fa0a,
    "priority-high": f603,
    "priority-low": f604,
    "professional-hexagon": f42d,
    "progress-alert": fc98,
    "progress-check": f994,
    "progress-clock": f995,
    "progress-download": f996,
    "progress-upload": f997,
    "progress-wrench": fc99,
    "projector": f42e,
    "projector-screen": f42f,
    "publish": f6a6,
    "pulse": f430,
    "pumpkin": fb9b,
    "puzzle": f431,
    "puzzle-outline": fa65,
    "qi": f998,
    "qqchat": f605,
    "qrcode": f432,
    "qrcode-edit": f8b7,
    "qrcode-scan": f433,
    "quadcopter": f434,
    "quality-high": f435,
    "quality-low": fa0b,
    "quality-medium": fa0c,
    "quicktime": f436,
    "quora": fd05,
    "rabbit": f906,
    "racing-helmet": fd6f,
    "racquetball": fd70,
    "radar": f437,
    "radiator": f438,
    "radiator-disabled": fad6,
    "radiator-off": fad7,
    "radio": f439,
    "radio-am": fc9a,
    "radio-fm": fc9b,
    "radio-handheld": f43a,
    "radio-tower": f43b,
    "radioactive": f43c,
    "radiobox-blank": f43d,
    "radiobox-marked": f43e,
    "radius": fc9c,
    "radius-outline": fc9d,
    "raspberry-pi": f43f,
    "ray-end": f440,
    "ray-end-arrow": f441,
    "ray-start": f442,
    "ray-start-arrow": f443,
    "ray-start-end": f444,
    "ray-vertex": f445,
    "react": f707,
    "read": f447,
    "receipt": f449,
    "record": f44a,
    "record-player": f999,
    "record-rec": f44b,
    "rectangle": fe41,
    "rectangle-outline": fe42,
    "recycle": f44c,
    "reddit": f44d,
    "redo": f44e,
    "redo-variant": f44f,
    "reflect-horizontal": fa0d,
    "reflect-vertical": fa0e,
    "refresh": f450,
    "regex": f451,
    "registered-trademark": fa66,
    "relative-scale": f452,
    "reload": f453,
    "reminder": f88b,
    "remote": f454,
    "remote-desktop": f8b8,
    "rename-box": f455,
    "reorder-horizontal": f687,
    "reorder-vertical": f688,
    "repeat": f456,
    "repeat-off": f457,
    "repeat-once": f458,
    "replay": f459,
    "reply": f45a,
    "reply-all": f45b,
    "reproduction": f45c,
    "resistor": fb1f,
    "resistor-nodes": fb20,
    "resize": fa67,
    "resize-bottom-right": f45d,
    "responsive": f45e,
    "restart": f708,
    "restart-off": fd71,
    "restore": f99a,
    "restore-clock": f6a7,
    "rewind": f45f,
    "rewind-10": fd06,
    "rewind-30": fd72,
    "rewind-outline": f709,
    "rhombus": f70a,
    "rhombus-medium": fa0f,
    "rhombus-outline": f70b,
    "rhombus-split": fa10,
    "ribbon": f460,
    "rice": f7e9,
    "ring": f7ea,
    "rivet": fe43,
    "road": f461,
    "road-variant": f462,
    "robot": f6a8,
    "robot-industrial": fb21,
    "robot-vacuum": f70c,
    "robot-vacuum-variant": f907,
    "rocket": f463,
    "roller-skate": fd07,
    "rollerblade": fd08,
    "rollupjs": fb9c,
    "room-service": f88c,
    "room-service-outline": fd73,
    "rotate-3d": f464,
    "rotate-left": f465,
    "rotate-left-variant": f466,
    "rotate-orbit": fd74,
    "rotate-right": f467,
    "rotate-right-variant": f468,
    "rounded-corner": f607,
    "router-wireless": f469,
    "router-wireless-settings": fa68,
    "routes": f46a,
    "rowing": f608,
    "rss": f46b,
    "rss-box": f46c,
    "ruby": fd09,
    "rugby": fd75,
    "ruler": f46d,
    "ruler-square": fc9e,
    "run": f70d,
    "run-fast": f46e,
    "sack": fd0a,
    "sack-percent": fd0b,
    "safe": fa69,
    "safety-goggles": fd0c,
    "sale": f46f,
    "salesforce": f88d,
    "sass": f7eb,
    "satellite": f470,
    "satellite-uplink": f908,
    "satellite-variant": f471,
    "sausage": f8b9,
    "saw-blade": fe44,
    "saxophone": f609,
    "scale": f472,
    "scale-balance": f5d1,
    "scale-bathroom": f473,
    "scanner": f6aa,
    "scanner-off": f909,
    "school": f474,
    "scissors-cutting": fa6a,
    "screen-rotation": f475,
    "screen-rotation-lock": f476,
    "screw-flat-top": fdcf,
    "screw-lag": fe54,
    "screw-machine-flat-top": fe55,
    "screw-machine-round-top": fe56,
    "screw-round-top": fe57,
    "screwdriver": f477,
    "script": fb9d,
    "script-outline": f478,
    "script-text": fb9e,
    "script-text-outline": fb9f,
    "sd": f479,
    "seal": f47a,
    "search-web": f70e,
    "seat": fc9f,
    "seat-flat": f47b,
    "seat-flat-angled": f47c,
    "seat-individual-suite": f47d,
    "seat-legroom-extra": f47e,
    "seat-legroom-normal": f47f,
    "seat-legroom-reduced": f480,
    "seat-outline": fca0,
    "seat-recline-extra": f481,
    "seat-recline-normal": f482,
    "seatbelt": fca1,
    "security": f483,
    "security-network": f484,
    "seed": fe45,
    "seed-outline": fe46,
    "select": f485,
    "select-all": f486,
    "select-color": fd0d,
    "select-compare": fad8,
    "select-drag": fa6b,
    "select-inverse": f487,
    "select-off": f488,
    "selection": f489,
    "selection-drag": fa6c,
    "selection-ellipse": fd0e,
    "selection-off": f776,
    "send": f48a,
    "send-circle": fe58,
    "send-circle-outline": fe59,
    "send-lock": f7ec,
    "serial-port": f65c,
    "server": f48b,
    "server-minus": f48c,
    "server-network": f48d,
    "server-network-off": f48e,
    "server-off": f48f,
    "server-plus": f490,
    "server-remove": f491,
    "server-security": f492,
    "set-all": f777,
    "set-center": f778,
    "set-center-right": f779,
    "set-left": f77a,
    "set-left-center": f77b,
    "set-left-right": f77c,
    "set-none": f77d,
    "set-right": f77e,
    "set-top-box": f99e,
    "settings": f493,
    "settings-box": f494,
    "settings-helper": fa6d,
    "settings-outline": f8ba,
    "shape": f830,
    "shape-circle-plus": f65d,
    "shape-outline": f831,
    "shape-plus": f495,
    "shape-polygon-plus": f65e,
    "shape-rectangle-plus": f65f,
    "shape-square-plus": f660,
    "share": f496,
    "share-outline": f931,
    "share-variant": f497,
    "sheep": fca2,
    "shield": f498,
    "shield-account": f88e,
    "shield-account-outline": fa11,
    "shield-airplane": f6ba,
    "shield-airplane-outline": fca3,
    "shield-check": f565,
    "shield-check-outline": fca4,
    "shield-cross": fca5,
    "shield-cross-outline": fca6,
    "shield-half-full": f77f,
    "shield-home": f689,
    "shield-home-outline": fca7,
    "shield-key": fba0,
    "shield-key-outline": fba1,
    "shield-link-variant": fd0f,
    "shield-link-variant-outline": fd10,
    "shield-lock": f99c,
    "shield-lock-outline": fca8,
    "shield-off": f99d,
    "shield-off-outline": f99b,
    "shield-outline": f499,
    "shield-plus": fad9,
    "shield-plus-outline": fada,
    "shield-remove": fadb,
    "shield-remove-outline": fadc,
    "shield-search": fd76,
    "ship-wheel": f832,
    "shoe-formal": fb22,
    "shoe-heel": fb23,
    "shoe-print": fe5a,
    "shopify": fadd,
    "shopping": f49a,
    "shopping-music": f49b,
    "shovel": f70f,
    "shovel-off": f710,
    "shower": f99f,
    "shower-head": f9a0,
    "shredder": f49c,
    "shuffle": f49d,
    "shuffle-disabled": f49e,
    "shuffle-variant": f49f,
    "sigma": f4a0,
    "sigma-lower": f62b,
    "sign-caution": f4a1,
    "sign-direction": f780,
    "sign-text": f781,
    "signal": f4a2,
    "signal-2g": f711,
    "signal-3g": f712,
    "signal-4g": f713,
    "signal-5g": fa6e,
    "signal-cellular-1": f8bb,
    "signal-cellular-2": f8bc,
    "signal-cellular-3": f8bd,
    "signal-cellular-outline": f8be,
    "signal-distance-variant": fe47,
    "signal-hspa": f714,
    "signal-hspa-plus": f715,
    "signal-off": f782,
    "signal-variant": f60a,
    "signature": fe5b,
    "signature-freehand": fe5c,
    "signature-image": fe5d,
    "signature-text": fe5e,
    "silo": fb24,
    "silverware": f4a3,
    "silverware-fork": f4a4,
    "silverware-fork-knife": fa6f,
    "silverware-spoon": f4a5,
    "silverware-variant": f4a6,
    "sim": f4a7,
    "sim-alert": f4a8,
    "sim-off": f4a9,
    "sina-weibo": fade,
    "sitemap": f4aa,
    "skate": fd11,
    "skew-less": fd12,
    "skew-more": fd13,
    "skip-backward": f4ab,
    "skip-forward": f4ac,
    "skip-next": f4ad,
    "skip-next-circle": f661,
    "skip-next-circle-outline": f662,
    "skip-previous": f4ae,
    "skip-previous-circle": f663,
    "skip-previous-circle-outline": f664,
    "skull": f68b,
    "skull-crossbones": fba2,
    "skull-crossbones-outline": fba3,
    "skull-outline": fba4,
    "skype": f4af,
    "skype-business": f4b0,
    "slack": f4b1,
    "slackware": f90a,
    "sleep": f4b2,
    "sleep-off": f4b3,
    "slope-downhill": fe5f,
    "slope-uphill": fe60,
    "smog": fa70,
    "smoke-detector": f392,
    "smoking": f4b4,
    "smoking-off": f4b5,
    "snapchat": f4b6,
    "snowflake": f716,
    "snowman": f4b7,
    "soccer": f4b8,
    "soccer-field": f833,
    "sofa": f4b9,
    "solar-panel": fd77,
    "solar-panel-large": fd78,
    "solar-power": fa71,
    "solid": f68c,
    "sort": f4ba,
    "sort-alphabetical": f4bb,
    "sort-ascending": f4bc,
    "sort-descending": f4bd,
    "sort-numeric": f4be,
    "sort-variant": f4bf,
    "sort-variant-lock": fca9,
    "sort-variant-lock-open": fcaa,
    "soundcloud": f4c0,
    "source-branch": f62c,
    "source-commit": f717,
    "source-commit-end": f718,
    "source-commit-end-local": f719,
    "source-commit-local": f71a,
    "source-commit-next-local": f71b,
    "source-commit-start": f71c,
    "source-commit-start-next-local": f71d,
    "source-fork": f4c1,
    "source-merge": f62d,
    "source-pull": f4c2,
    "source-repository": fcab,
    "source-repository-multiple": fcac,
    "soy-sauce": f7ed,
    "spa": fcad,
    "spa-outline": fcae,
    "space-invaders": fba5,
    "spade": fe48,
    "speaker": f4c3,
    "speaker-bluetooth": f9a1,
    "speaker-multiple": fd14,
    "speaker-off": f4c4,
    "speaker-wireless": f71e,
    "speedometer": f4c5,
    "spellcheck": f4c6,
    "spider-web": fba6,
    "spotify": f4c7,
    "spotlight": f4c8,
    "spotlight-beam": f4c9,
    "spray": f665,
    "spray-bottle": fadf,
    "sprout": fe49,
    "sprout-outline": fe4a,
    "square": f763,
    "square-edit-outline": f90b,
    "square-inc": f4ca,
    "square-inc-cash": f4cb,
    "square-medium": fa12,
    "square-medium-outline": fa13,
    "square-outline": f762,
    "square-root": f783,
    "square-root-box": f9a2,
    "square-small": fa14,
    "squeegee": fae0,
    "ssh": f8bf,
    "stack-exchange": f60b,
    "stack-overflow": f4cc,
    "stadium": f71f,
    "stairs": f4cd,
    "stamper": fd15,
    "standard-definition": f7ee,
    "star": f4ce,
    "star-box": fa72,
    "star-box-outline": fa73,
    "star-circle": f4cf,
    "star-circle-outline": f9a3,
    "star-face": f9a4,
    "star-four-points": fae1,
    "star-four-points-outline": fae2,
    "star-half": f4d0,
    "star-off": f4d1,
    "star-outline": f4d2,
    "star-three-points": fae3,
    "star-three-points-outline": fae4,
    "steam": f4d3,
    "steam-box": f90c,
    "steering": f4d4,
    "steering-off": f90d,
    "step-backward": f4d5,
    "step-backward-2": f4d6,
    "step-forward": f4d7,
    "step-forward-2": f4d8,
    "stethoscope": f4d9,
    "sticker": f5d0,
    "sticker-emoji": f784,
    "stocking": f4da,
    "stop": f4db,
    "stop-circle": f666,
    "stop-circle-outline": f667,
    "store": f4dc,
    "store-24-hour": f4dd,
    "stove": f4de,
    "strava": fb25,
    "subdirectory-arrow-left": f60c,
    "subdirectory-arrow-right": f60d,
    "subtitles": fa15,
    "subtitles-outline": fa16,
    "subway": f6ab,
    "subway-alert-variant": fd79,
    "subway-variant": f4df,
    "summit": f785,
    "sunglasses": f4e0,
    "surround-sound": f5c5,
    "surround-sound-2-0": f7ef,
    "surround-sound-3-1": f7f0,
    "surround-sound-5-1": f7f1,
    "surround-sound-7-1": f7f2,
    "svg": f720,
    "swap-horizontal": f4e1,
    "swap-horizontal-bold": fba9,
    "swap-horizontal-variant": f8c0,
    "swap-vertical": f4e2,
    "swap-vertical-bold": fbaa,
    "swap-vertical-variant": f8c1,
    "swim": f4e3,
    "switch": f4e4,
    "sword": f4e5,
    "sword-cross": f786,
    "symfony": fae5,
    "sync": f4e6,
    "sync-alert": f4e7,
    "sync-off": f4e8,
    "tab": f4e9,
    "tab-minus": fb26,
    "tab-plus": f75b,
    "tab-remove": fb27,
    "tab-unselected": f4ea,
    "table": f4eb,
    "table-border": fa17,
    "table-column": f834,
    "table-column-plus-after": f4ec,
    "table-column-plus-before": f4ed,
    "table-column-remove": f4ee,
    "table-column-width": f4ef,
    "table-edit": f4f0,
    "table-large": f4f1,
    "table-merge-cells": f9a5,
    "table-of-contents": f835,
    "table-plus": fa74,
    "table-remove": fa75,
    "table-row": f836,
    "table-row-height": f4f2,
    "table-row-plus-after": f4f3,
    "table-row-plus-before": f4f4,
    "table-row-remove": f4f5,
    "table-search": f90e,
    "table-settings": f837,
    "table-tennis": fe4b,
    "tablet": f4f6,
    "tablet-android": f4f7,
    "tablet-cellphone": f9a6,
    "tablet-ipad": f4f8,
    "taco": f761,
    "tag": f4f9,
    "tag-faces": f4fa,
    "tag-heart": f68a,
    "tag-heart-outline": fbab,
    "tag-minus": f90f,
    "tag-multiple": f4fb,
    "tag-outline": f4fc,
    "tag-plus": f721,
    "tag-remove": f722,
    "tag-text-outline": f4fd,
    "tank": fd16,
    "tape-measure": fb28,
    "target": f4fe,
    "target-account": fbac,
    "target-variant": fa76,
    "taxi": f4ff,
    "tea": fd7a,
    "tea-outline": fd7b,
    "teach": f88f,
    "teamviewer": f500,
    "telegram": f501,
    "telescope": fb29,
    "television": f502,
    "television-box": f838,
    "television-classic": f7f3,
    "television-classic-off": f839,
    "television-guide": f503,
    "television-off": f83a,
    "temperature-celsius": f504,
    "temperature-fahrenheit": f505,
    "temperature-kelvin": f506,
    "tennis": fd7c,
    "tennis-ball": f507,
    "tent": f508,
    "terrain": f509,
    "test-tube": f668,
    "test-tube-empty": f910,
    "test-tube-off": f911,
    "text": f9a7,
    "text-shadow": f669,
    "text-short": f9a8,
    "text-subject": f9a9,
    "text-to-speech": f50a,
    "text-to-speech-off": f50b,
    "textbox": f60e,
    "textbox-password": f7f4,
    "texture": f50c,
    "theater": f50d,
    "theme-light-dark": f50e,
    "thermometer": f50f,
    "thermometer-alert": fe61,
    "thermometer-chevron-down": fe62,
    "thermometer-chevron-up": fe63,
    "thermometer-lines": f510,
    "thermometer-minus": fe64,
    "thermometer-plus": fe65,
    "thermostat": f393,
    "thermostat-box": f890,
    "thought-bubble": f7f5,
    "thought-bubble-outline": f7f6,
    "thumb-down": f511,
    "thumb-down-outline": f512,
    "thumb-up": f513,
    "thumb-up-outline": f514,
    "thumbs-up-down": f515,
    "ticket": f516,
    "ticket-account": f517,
    "ticket-confirmation": f518,
    "ticket-outline": f912,
    "ticket-percent": f723,
    "tie": f519,
    "tilde": f724,
    "timelapse": f51a,
    "timeline": fbad,
    "timeline-outline": fbae,
    "timeline-text": fbaf,
    "timeline-text-outline": fbb0,
    "timer": f51b,
    "timer-10": f51c,
    "timer-3": f51d,
    "timer-off": f51e,
    "timer-sand": f51f,
    "timer-sand-empty": f6ac,
    "timer-sand-full": f78b,
    "timetable": f520,
    "toaster-oven": fcaf,
    "toggle-switch": f521,
    "toggle-switch-off": f522,
    "toggle-switch-off-outline": fa18,
    "toggle-switch-outline": fa19,
    "toilet": f9aa,
    "toolbox": f9ab,
    "toolbox-outline": f9ac,
    "tooltip": f523,
    "tooltip-account": f00c,
    "tooltip-edit": f524,
    "tooltip-image": f525,
    "tooltip-image-outline": fbb1,
    "tooltip-outline": f526,
    "tooltip-plus": fbb2,
    "tooltip-plus-outline": f527,
    "tooltip-text": f528,
    "tooltip-text-outline": fbb3,
    "tooth": f8c2,
    "tooth-outline": f529,
    "tor": f52a,
    "tortoise": fd17,
    "tournament": f9ad,
    "tower-beach": f680,
    "tower-fire": f681,
    "towing": f83b,
    "track-light": f913,
    "trackpad": f7f7,
    "trackpad-lock": f932,
    "tractor": f891,
    "trademark": fa77,
    "traffic-light": f52b,
    "train": f52c,
    "train-car": fbb4,
    "train-variant": f8c3,
    "tram": f52d,
    "transcribe": f52e,
    "transcribe-close": f52f,
    "transfer-down": fd7d,
    "transfer-left": fd7e,
    "transfer-right": f530,
    "transfer-up": fd7f,
    "transit-connection": fd18,
    "transit-connection-variant": fd19,
    "transit-transfer": f6ad,
    "transition": f914,
    "transition-masked": f915,
    "translate": f5ca,
    "translate-off": fe66,
    "transmission-tower": fd1a,
    "trash-can": fa78,
    "trash-can-outline": fa79,
    "treasure-chest": f725,
    "tree": f531,
    "tree-outline": fe4c,
    "trello": f532,
    "trending-down": f533,
    "trending-neutral": f534,
    "trending-up": f535,
    "triangle": f536,
    "triangle-outline": f537,
    "triforce": fbb5,
    "trophy": f538,
    "trophy-award": f539,
    "trophy-broken": fd80,
    "trophy-outline": f53a,
    "trophy-variant": f53b,
    "trophy-variant-outline": f53c,
    "truck": f53d,
    "truck-check": fcb0,
    "truck-delivery": f53e,
    "truck-fast": f787,
    "truck-trailer": f726,
    "tshirt-crew": fa7a,
    "tshirt-crew-outline": f53f,
    "tshirt-v": fa7b,
    "tshirt-v-outline": f540,
    "tumble-dryer": f916,
    "tumblr": f541,
    "tumblr-box": f917,
    "tumblr-reblog": f542,
    "tune": f62e,
    "tune-vertical": f66a,
    "turnstile": fcb1,
    "turnstile-outline": fcb2,
    "turtle": fcb3,
    "twitch": f543,
    "twitter": f544,
    "twitter-box": f545,
    "twitter-circle": f546,
    "twitter-retweet": f547,
    "two-factor-authentication": f9ae,
    "uber": f748,
    "ubisoft": fbb6,
    "ubuntu": f548,
    "ultra-high-definition": f7f8,
    "umbraco": f549,
    "umbrella": f54a,
    "umbrella-closed": f9af,
    "umbrella-outline": f54b,
    "undo": f54c,
    "undo-variant": f54d,
    "unfold-less-horizontal": f54e,
    "unfold-less-vertical": f75f,
    "unfold-more-horizontal": f54f,
    "unfold-more-vertical": f760,
    "ungroup": f550,
    "unity": f6ae,
    "unreal": f9b0,
    "untappd": f551,
    "update": f6af,
    "upload": f552,
    "upload-multiple": f83c,
    "upload-network": f6f5,
    "upload-network-outline": fcb4,
    "upload-outline": fe67,
    "usb": f553,
    "van-passenger": f7f9,
    "van-utility": f7fa,
    "vanish": f7fb,
    "variable": fae6,
    "vector-arrange-above": f554,
    "vector-arrange-below": f555,
    "vector-bezier": fae7,
    "vector-circle": f556,
    "vector-circle-variant": f557,
    "vector-combine": f558,
    "vector-curve": f559,
    "vector-difference": f55a,
    "vector-difference-ab": f55b,
    "vector-difference-ba": f55c,
    "vector-ellipse": f892,
    "vector-intersection": f55d,
    "vector-line": f55e,
    "vector-point": f55f,
    "vector-polygon": f560,
    "vector-polyline": f561,
    "vector-radius": f749,
    "vector-rectangle": f5c6,
    "vector-selection": f562,
    "vector-square": f001,
    "vector-triangle": f563,
    "vector-union": f564,
    "venmo": f578,
    "vhs": fa1a,
    "vibrate": f566,
    "vibrate-off": fcb5,
    "video": f567,
    "video-3d": f7fc,
    "video-4k-box": f83d,
    "video-account": f918,
    "video-image": f919,
    "video-input-antenna": f83e,
    "video-input-component": f83f,
    "video-input-hdmi": f840,
    "video-input-svideo": f841,
    "video-minus": f9b1,
    "video-off": f568,
    "video-off-outline": fbb7,
    "video-outline": fbb8,
    "video-plus": f9b2,
    "video-stabilization": f91a,
    "video-switch": f569,
    "video-vintage": fa1b,
    "view-agenda": f56a,
    "view-array": f56b,
    "view-carousel": f56c,
    "view-column": f56d,
    "view-comfy": fe4d,
    "view-compact": fe4e,
    "view-compact-outline": fe4f,
    "view-dashboard": f56e,
    "view-dashboard-outline": fa1c,
    "view-dashboard-variant": f842,
    "view-day": f56f,
    "view-grid": f570,
    "view-headline": f571,
    "view-list": f572,
    "view-module": f573,
    "view-parallel": f727,
    "view-quilt": f574,
    "view-sequential": f728,
    "view-split-horizontal": fba7,
    "view-split-vertical": fba8,
    "view-stream": f575,
    "view-week": f576,
    "vimeo": f577,
    "violin": f60f,
    "virtual-reality": f893,
    "visual-studio": f610,
    "visual-studio-code": fa1d,
    "vk": f579,
    "vk-box": f57a,
    "vk-circle": f57b,
    "vlc": f57c,
    "voice": f5cb,
    "voicemail": f57d,
    "volleyball": f9b3,
    "volume-high": f57e,
    "volume-low": f57f,
    "volume-medium": f580,
    "volume-minus": f75d,
    "volume-mute": f75e,
    "volume-off": f581,
    "volume-plus": f75c,
    "volume-variant-off": fe68,
    "vote": fa1e,
    "vote-outline": fa1f,
    "vpn": f582,
    "vuejs": f843,
    "vuetify": fe50,
    "walk": f583,
    "wall": f7fd,
    "wall-sconce": f91b,
    "wall-sconce-flat": f91c,
    "wall-sconce-variant": f91d,
    "wallet": f584,
    "wallet-giftcard": f585,
    "wallet-membership": f586,
    "wallet-outline": fbb9,
    "wallet-travel": f587,
    "wallpaper": fe69,
    "wan": f588,
    "washing-machine": f729,
    "watch": f589,
    "watch-export": f58a,
    "watch-export-variant": f894,
    "watch-import": f58b,
    "watch-import-variant": f895,
    "watch-variant": f896,
    "watch-vibrate": f6b0,
    "watch-vibrate-off": fcb6,
    "water": f58c,
    "water-off": f58d,
    "water-outline": fe6a,
    "water-percent": f58e,
    "water-pump": f58f,
    "watermark": f612,
    "waves": f78c,
    "waze": fbba,
    "weather-cloudy": f590,
    "weather-cloudy-arrow-right": fe51,
    "weather-fog": f591,
    "weather-hail": f592,
    "weather-hurricane": f897,
    "weather-lightning": f593,
    "weather-lightning-rainy": f67d,
    "weather-night": f594,
    "weather-partlycloudy": f595,
    "weather-pouring": f596,
    "weather-rainy": f597,
    "weather-snowy": f598,
    "weather-snowy-rainy": f67e,
    "weather-sunny": f599,
    "weather-sunset": f59a,
    "weather-sunset-down": f59b,
    "weather-sunset-up": f59c,
    "weather-windy": f59d,
    "weather-windy-variant": f59e,
    "web": f59f,
    "webcam": f5a0,
    "webhook": f62f,
    "webpack": f72a,
    "wechat": f611,
    "weight": f5a1,
    "weight-gram": fd1b,
    "weight-kilogram": f5a2,
    "weight-pound": f9b4,
    "whatsapp": f5a3,
    "wheelchair-accessibility": f5a4,
    "whistle": f9b5,
    "white-balance-auto": f5a5,
    "white-balance-incandescent": f5a6,
    "white-balance-iridescent": f5a7,
    "white-balance-sunny": f5a8,
    "widgets": f72b,
    "wifi": f5a9,
    "wifi-off": f5aa,
    "wifi-star": fe6b,
    "wifi-strength-1": f91e,
    "wifi-strength-1-alert": f91f,
    "wifi-strength-1-lock": f920,
    "wifi-strength-2": f921,
    "wifi-strength-2-alert": f922,
    "wifi-strength-2-lock": f923,
    "wifi-strength-3": f924,
    "wifi-strength-3-alert": f925,
    "wifi-strength-3-lock": f926,
    "wifi-strength-4": f927,
    "wifi-strength-4-alert": f928,
    "wifi-strength-4-lock": f929,
    "wifi-strength-alert-outline": f92a,
    "wifi-strength-lock-outline": f92b,
    "wifi-strength-off": f92c,
    "wifi-strength-off-outline": f92d,
    "wifi-strength-outline": f92e,
    "wii": f5ab,
    "wiiu": f72c,
    "wikipedia": f5ac,
    "wind-turbine": fd81,
    "window-close": f5ad,
    "window-closed": f5ae,
    "window-maximize": f5af,
    "window-minimize": f5b0,
    "window-open": f5b1,
    "window-restore": f5b2,
    "windows": f5b3,
    "windows-classic": fa20,
    "wiper": fae8,
    "wiper-wash": fd82,
    "wordpress": f5b4,
    "worker": f5b5,
    "wrap": f5b6,
    "wrap-disabled": fbbb,
    "wrench": f5b7,
    "wrench-outline": fbbc,
    "wunderlist": f5b8,
    "xamarin": f844,
    "xamarin-outline": f845,
    "xaml": f673,
    "xbox": f5b9,
    "xbox-controller": f5ba,
    "xbox-controller-battery-alert": f74a,
    "xbox-controller-battery-charging": fa21,
    "xbox-controller-battery-empty": f74b,
    "xbox-controller-battery-full": f74c,
    "xbox-controller-battery-low": f74d,
    "xbox-controller-battery-medium": f74e,
    "xbox-controller-battery-unknown": f74f,
    "xbox-controller-menu": fe52,
    "xbox-controller-off": f5bb,
    "xbox-controller-view": fe53,
    "xda": f5bc,
    "xing": f5bd,
    "xing-box": f5be,
    "xing-circle": f5bf,
    "xml": f5c0,
    "xmpp": f7fe,
    "yahoo": fb2a,
    "yammer": f788,
    "yeast": f5c1,
    "yelp": f5c2,
    "yin-yang": f67f,
    "youtube": f5c3,
    "youtube-creator-studio": f846,
    "youtube-gaming": f847,
    "youtube-subscription": fd1c,
    "youtube-tv": f448,
    "z-wave": fae9,
    "zend": faea,
    "zigbee": fd1d,
    "zip-box": f5c4,
    "zip-disk": fa22,
    "zodiac-aquarius": fa7c,
    "zodiac-aries": fa7d,
    "zodiac-cancer": fa7e,
    "zodiac-capricorn": fa7f,
    "zodiac-gemini": fa80,
    "zodiac-leo": fa81,
    "zodiac-libra": fa82,
    "zodiac-pisces": fa83,
    "zodiac-sagittarius": fa84,
    "zodiac-scorpio": fa85,
    "zodiac-taurus": fa86,
    "zodiac-virgo": fa87
);