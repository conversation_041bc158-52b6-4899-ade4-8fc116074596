
/* HELPER CLASS
 * -------------------------- */

/* FA based classes */

/*! Modified from font-awesome helper CSS classes - PIXEDEN
 *  Font Awesome 4.0.3 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (CSS: MIT License)
 */

/* makes the font 33% larger relative to the icon container */
.pe-lg {
  font-size: $font-size-base * 1.33;
  line-height: 0.75em;
  vertical-align: -15%;
}

.pe-2x {
  font-size: 2 * $font-size-base;
}

.pe-3x {
  font-size: 3 * $font-size-base;
}

.pe-4x {
  font-size: 4 * $font-size-base;
}

.pe-5x {
  font-size: 5 * $font-size-base;
}

.pe-fw {
  width: 1.2857142857142858em;
  text-align: center;
}

.pe-ul {
  padding-left: 0;
  margin-left: 2.142857142857143em;
  list-style-type: none;

  & > li {
    position: relative;
  }
}

.pe-li {
  position: absolute;
  left: -2.142857142857143em;
  width: 2.142857142857143em;
  top: 0.14285714285714285em;
  text-align: center;

  &.pe-lg {
    left: -1.8571428571428572em;
  }
}

.pe-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eee;
  border-radius: .1em;
}

.pull-right {
  float: right;
}

.pe {
  &.pull-left {
    float: left;
    margin-right: .3em;
    margin-left: .3em;
  }
}

.pe-spin {
  animation: spin 2s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(359deg);
  }
}

.pe-rotate-90 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
  transform: rotate(90deg);
}

.pe-rotate-180 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  transform: rotate(180deg);
}

.pe-rotate-270 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
  transform: rotate(270deg);
}

.pe-flip-horizontal {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
  transform: scale(-1, 1);
}

.pe-flip-vertical {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
  transform: scale(1, -1);
}

.pe-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

.pe-stack-1x,
.pe-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

.pe-stack-1x {
  line-height: inherit;
}

.pe-stack-2x {
  font-size: 2em;
}

.pe-inverse {
  color: #fff;
}

/* Custom classes / mods - PIXEDEN */
.pe-va {
  vertical-align: middle;
}

.pe-border {
  border: solid 0.08em #eaeaea;
}
