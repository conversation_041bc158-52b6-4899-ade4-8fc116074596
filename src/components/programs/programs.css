.file-display-container {
  display: flex;
  align-items: center;
  text-align: left;
  flex: 1;
  color: #5f77a5 !important;
  font-family: 'Helvetica', sans-serif;
  font-size: 20px;
  font-weight: 600;
  border: 2px dotted #eff1f6;
  min-height: 120px;
  border-radius: 4px;
  position: relative;
  padding: 1em;
  height: 90%;
}

.pdf-file-display-container {
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: left;
  background: #f4f4f4;
  overflow-x: hidden;
  position: relative;
  padding: 20px;
  border-radius: 12px;
}

.react-pdf__Document {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.react-pdf__Page {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: white;
}

.react-pdf__Page__canvas {
  width: 100% !important;
  height: 100% !important;
}

.textLayer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: none;
}

.annotationLayer {
  position: absolute;
  top: 10;
  left: 10;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.expand-icon {
  position: absolute;
  display: flex;
  align-items: center;
  color: black;
  top: 3px;
  right: 3px;
  z-index: 10;
  background: white;
  border-radius: 22%;
  padding: 4px;
}

/* Hide asterisks on required fields */
.required-field-indicator,
label.required::after,
.form-label.required::after,
.form-label.required:after,
label.required:after {
  display: none !important;
}

/* Additional selectors to ensure all asterisks are hidden */
.form-label[required]::after,
.form-label[required]:after,
label[required]::after,
label[required]:after {
  display: none !important;
}

/* Add styles for the PDF container to indicate right-click functionality */
.pdf-file-display-container .pdf-container {
  position: relative;
}

.pdf-file-display-container .pdf-container::after {
  content: "Right-click to access links";
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(44, 68, 116, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.pdf-file-display-container .pdf-container:hover::after {
  opacity: 1;
}

/* Improve the context menu appearance */
.MuiMenu-paper {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  border-radius: 8px !important;
}

.MuiMenuItem-root {
  min-width: 200px;
}

.MuiMenuItem-root .MuiSvgIcon-root {
  color: #2C4474;
}
