/* eslint-disable no-nested-ternary */
import { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Form, Button } from 'react-bootstrap';
import { Plus, Check, X as CloseIcon } from 'react-feather';
import { useNavigate } from 'react-router';
import dayjs from 'dayjs';
import Dropzone, { IFileWithMeta, IUploadParams, StatusValue } from 'react-dropzone-uploader';
import { tossError, tossSuccess } from 'utils/toastTosser';
import { KeyboardArrowDown, KeyboardArrowUp, ArrowBack } from '@mui/icons-material';
import { TextInput, FunderInput, MoneyInputNew, MultiSelectInput } from 'shared/inputs';
import CustomFields from 'shared/CustomFields';
import useUserSession from 'hooks/useUserSession';
import DateField from 'components/applications/EditForm/DateField';
import { createProgram } from 'services/programService';
import api from 'services/apiService';
import { nanoid } from 'nanoid';
import './programs.css';
import 'assets/scss/programDetailsnew.scss';
import { useProgramContext } from 'hooks/ProgramContext';
import { Box, Stack, IconButton } from '@mui/material';
import {
  ALL_COUNTIES_OPTION,
  ALL_STATES_OPTION,
  ALL_ORG_TYPES_OPTION, // Add this constant
  applicationsConfigs,
  getCountiesForStates,
  OrgTypes,
  states,
  statesWithAllOption,
} from '../../constants/globalConstants';

export default function CreateProgramNew() {
  const [form, setForm] = useState({
    showForFlexClient: true,
    orgTypes: [...OrgTypes], // Pre-populate with all specific org types: ['Municipality', 'Educational', 'Nonprofit']
    states: [ALL_STATES_OPTION],
    counties: [ALL_COUNTIES_OPTION],
    source: [],
    category: [],
  });
  const [errors, setErrors] = useState({});
  const [files, setFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSectionVisible, setIsSectionVisible] = useState(true);
  const tempProgram = parseInt(nanoid(6).replace(/\D/g, ''), 10);
  const setField = (field, value) => setForm((prevState) => ({ ...prevState, [field]: value }));
  const [tempProgramId, setTempProgramId] = useState(tempProgram || 0);
  const isFromPrograms = true;
  const navigate = useNavigate();
  const { createProgramProps } = useProgramContext();
  const { readOnly, disabled, onCreation } = createProgramProps;
  const currentUser = useUserSession();

  const save = async () => {
    setIsLoading(true);
    try {
      const {
        name,
        funder,
        fundingAmount,
        varyingFundingAmount,
        amountVaries,
        source,
        category,
        startsAt,
        endsAt,
        matchRequirements,
        performancePeriod,
        estimatedResponse,
        customFields,
        states: selectedStates,
        counties: selectedCounties,
        orgTypes,
        showForFlexClient,
      } = form;

      const submittedFields = {
        name,
        funder,
        fundingAmount: fundingAmount ?? 0.0,
        varyingFundingAmount,
        amountVaries: amountVaries ?? false,
        source,
        startsAt,
        endsAt,
        matchRequirements,
        performancePeriod,
        estimatedResponse,
        customFields: JSON.stringify(customFields ?? []),
        category: category.map((cat) => applicationsConfigs.categories.indexOf(cat)),
        states: selectedStates,
        counties: selectedCounties,
        orgTypes, // No special handling needed - just use the selected org types
        showForFlexClient: showForFlexClient ?? true,
      };

      const newProgram = await createProgram(submittedFields);
      if (newProgram) {
        if (files.length > 0) {
          await api.patch('/programs/updateFileProgramId', {
            tempProgramId,
            programId: newProgram.id,
          });
        }

        tossSuccess('The program was successfully created.');
        setForm({
          showForFlexClient: true,
          orgTypes: [],
          states: [],
          counties: [],
          source: [],
          category: [],
        });
        setFiles([]);
        if (onCreation) onCreation();
        navigate(-1);
      } else {
        tossError('Error creating the program.');
      }
    } catch (error) {
      tossError('Error creating the program.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleDiscard = () => {
    setForm({
      showForFlexClient: true,
      orgTypes: [...OrgTypes], // Reset to all specific org types
      states: [ALL_STATES_OPTION],
      counties: [ALL_COUNTIES_OPTION],
      source: [],
      category: [],
    });
    setFiles([]);
    setErrors({});
    setTempProgramId(tempProgram);
  };

  const toggleSectionVisibility = () => {
    setIsSectionVisible((prev) => !prev);
  };

  const getUploadParams = (fileWithMeta: IFileWithMeta) => {
    const body = new FormData();
    body.append('programId', tempProgramId.toString());
    body.append('file', fileWithMeta.file);
    body.append('size', fileWithMeta.meta.size.toString());
    body.append('name', fileWithMeta.meta.name);
    body.append('fileUrl', fileWithMeta.meta.previewUrl || '');
    body.append('type', fileWithMeta.meta.type);

    return {
      url: `${process.env.REACT_APP_API_URL}/programs/programfile`,
      body,
      headers: {
        Authorization: api.client?.defaults.headers.common.Authorization,
      },
    } as IUploadParams;
  };

  const handleChangeStatus = (fileWithMeta: IFileWithMeta, status: StatusValue) => {
    if (status === 'done') {
      fileWithMeta.remove();
      setFiles((prevFiles) => [
        ...prevFiles,
        {
          id: fileWithMeta.meta.id,
          name: fileWithMeta.meta.name,
          size: fileWithMeta.meta.size,
          type: fileWithMeta.meta.type,
        },
      ]);
    } else if (status === 'error_upload_params') {
      tossError('Error uploading file. Please try again.');
    }
  };

  const handleDeleteFile = async (fileId: number) => {
    setFiles([]);
  };

  useEffect(() => {
    const {
      name,
      funder,
      fundingAmount,
      amountVaries,
      source,
      category,
      startsAt,
      endsAt,
      matchRequirements,
      performancePeriod,
      estimatedResponse,
      orgTypes,
      states,
      counties,
    } = form;

    const errorList = {};

    if (typeof name !== 'undefined') {
      if (!name) errorList.name = 'This field is required.';
      else if (name.length < 3)
        errorList.name = 'Grant program names must have 3 or more characters.';
    }

    if (!startsAt) errorList.startsAt = 'This field is required.';
    if (!endsAt) errorList.endsAt = 'This field is required.';

    if (typeof startsAt !== 'undefined' && typeof endsAt !== 'undefined') {
      if (startsAt && endsAt) {
        if (dayjs(endsAt).isBefore(dayjs(startsAt))) {
          errorList.startsAt = 'Start Date must be earlier than Due Date.';
          errorList.endsAt = 'Due Date must be later than Start Date.';
        }
      }
    }

    if (!funder) errorList.funder = 'This field is required.';
    if (Number.isNaN(fundingAmount) && !amountVaries)
      errorList.fundingAmount = 'Enter a valid funding amount.';
    if (!source || source.length === 0) errorList.source = 'This field is required.';
    if (!category || category.length === 0) errorList.category = 'This field is required.';
    if (!matchRequirements) errorList.matchRequirements = 'This field is required.';
    if (!performancePeriod) errorList.performancePeriod = 'This field is required.';
    if (!estimatedResponse) errorList.estimatedResponse = 'This field is required.';
    // if (!orgTypes || orgTypes.length === 0) errorList.orgTypes = 'This field is required.';
    // if (!states || states.length === 0) errorList.states = 'This field is required.';
    // if (!counties || counties.length === 0) errorList.counties = 'This field is required.';

    setErrors(errorList);
  }, [form]);

  return (
    <Container className="create-program-page" fluid>
      <Stack alignItems="space-between" direction="column" mb={4} spacing={1}>
        <Box mb={2}>
          <Stack alignItems="center" direction="row" spacing={1}>
            <span
              className="breadcrumb-list"
              onClick={() => navigate(-1)}
              onKeyDown={(e) => e.key === 'Enter' && navigate(-1)}
              role="button"
              style={{ textDecoration: 'underline', cursor: 'pointer' }}
              tabIndex={0}
            >
              Programs
            </span>
            <span>{'>'}</span>
            <span className="breadcrumb-list" style={{ fontWeight: '700' }}>
              New Program Details
            </span>
          </Stack>
        </Box>
        <Stack alignItems="center" direction="row" justifyContent="space-between" mb={2}>
          <Stack alignItems="center" direction="row" spacing={1}>
            <IconButton
              className="back-button"
              onClick={() => navigate(-1)}
              sx={{
                width: '34px',
                height: '34px',
                backgroundColor: '#eff1f6',
                borderRadius: '6px',
                padding: '8px 16px',
                '&:hover': {
                  backgroundColor: '#e0e3eb',
                },
              }}
            >
              <ArrowBack />
            </IconButton>

            <h5 className="heading">Create New Program</h5>
          </Stack>
          <Stack direction="row" spacing={1}>
            <Button className="discard-btn" onClick={handleDiscard} variant="light">
              Discard
            </Button>
            <Button
              className="d-flex justify-content-center align-items-center"
              disabled={Object.keys(errors).length > 0 || isLoading}
              onClick={save}
            >
              <Check size={16} />
              &nbsp;Create
            </Button>
          </Stack>
        </Stack>
      </Stack>
      <Form>
        <div className="general-fields-wrapper">
          <Row>
            <Col lg={6} xs={12}>
              <h5 className="heading-gb mb-4">Grant Summary File</h5>
              <div className="file-uploader">
                {files?.length > 0 ? (
                  <div className="file-display-container">
                    <a
                      className="file-display-link"
                      download={files[0].name}
                      href={files[0].fileUrl}
                      rel="noopener noreferrer"
                      target="_blank"
                    >
                      {files[0].name}
                    </a>
                    <CloseIcon
                      className="file-display-close"
                      onClick={() => handleDeleteFile(files[0].id)}
                      size={24}
                    />
                  </div>
                ) : (
                  <div className="dz-message tall needsclick">
                    <Dropzone
                      disabled={readOnly}
                      getUploadParams={getUploadParams}
                      inputContent="Drag Files Here"
                      onChangeStatus={handleChangeStatus}
                    />
                  </div>
                )}
              </div>
            </Col>
            <Col lg={6} xs={12}>
              <h5 className="heading-gb mb-4">Required Fields</h5>
              <FunderInput
                className="common-input"
                controlId="createProgram.Funder"
                errors={errors.funder}
                hideRequiredIndicator
                onChange={(newValue) => setField('funder', newValue)}
                placeholder="Start typing a name..."
              />

              <Row>
                <Col lg={6} xs={12}>
                  <TextInput
                    className="common-input"
                    controlId="createProgram.Name"
                    errors={errors.name}
                    hideRequiredIndicator
                    label="Grant Program Name"
                    onChange={(newValue) => setField('name', newValue)}
                    placeholder="My Grant Program"
                    required
                  />
                </Col>
                <Col lg={6} xs={12}>
                  <MultiSelectInput
                    key={`org-types-${JSON.stringify(form.orgTypes)}`}
                    choices={OrgTypes} // Remove the "All Types" option from choices
                    className="common-input"
                    controlId="createProgram.OrgTypes"
                    defaultValue={form.orgTypes.length ? form.orgTypes : [...OrgTypes]} // Default to all specific types
                    disabled={readOnly}
                    isIndexBased={false}
                    label="Organization Type"
                    onChange={(selectedIndices) => {
                      // If no selections, default to all org types
                      if (selectedIndices.length === 0) {
                        setField('orgTypes', [...OrgTypes]);
                        return;
                      }

                      // Convert indices back to actual values
                      const selectedValues = selectedIndices.map((index) => OrgTypes[index]);

                      // Use the selection as is
                      setField('orgTypes', selectedValues);
                    }}
                  />
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  <DateField
                    className="common-input"
                    controlId="createProgram.StartDate"
                    errors={errors.startsAt}
                    label="Start Date"
                    onChange={(newValue) => setField('startsAt', newValue)}
                    value={form.startsAt}
                  />
                </Col>
                <Col lg={6} xs={12}>
                  <DateField
                    className="common-input"
                    controlId="createProgram.DueDate"
                    errors={errors.endsAt}
                    label="Due Date"
                    onChange={(newValue) => setField('endsAt', newValue)}
                    required
                    value={form.endsAt}
                  />
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  <MultiSelectInput
                    key={`category-${JSON.stringify(form.category)}`}
                    choices={applicationsConfigs.alphabetSortedCategories}
                    className="common-input"
                    controlId="createProgram.Category"
                    defaultValue={form.category}
                    disabled={readOnly}
                    isIndexBased={false}
                    label="Category"
                    onChange={(selectedIndices) => {
                      console.log('Category - Selected indices:', selectedIndices);

                      // Handle empty selection case
                      if (selectedIndices.length === 0) {
                        console.log('Category - No categories selected');
                        setField('category', []);
                        return;
                      }

                      // Convert indices to actual category values
                      const selectedCategories = selectedIndices.map(
                        (index) => applicationsConfigs.alphabetSortedCategories[index]
                      );
                      console.log('Category - Selected values:', selectedCategories);

                      setField('category', selectedCategories);
                    }}
                    required
                  />
                </Col>
                <Col lg={6} xs={12}>
                  <MultiSelectInput
                    key={`source-${JSON.stringify(form.source)}`}
                    choices={applicationsConfigs.sources}
                    className="common-input"
                    controlId="createProgram.Source"
                    defaultValue={form.source}
                    disabled={readOnly}
                    isIndexBased
                    label="Source"
                    onChange={(selectedIndices) => {
                      console.log('Source - Selected indices:', selectedIndices);

                      // Handle empty selection case
                      if (selectedIndices.length === 0) {
                        console.log('Source - No sources selected');
                        setField('source', []);
                        return;
                      }

                      setField('source', selectedIndices);
                    }}
                    required
                  />
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <MultiSelectInput
                      key={`states-${JSON.stringify(form.states)}`}
                      choices={statesWithAllOption.map((state) => state.name)}
                      className="common-input"
                      controlId="createProgram.States"
                      defaultValue={form.states.length ? form.states : [ALL_STATES_OPTION]}
                      disabled={readOnly}
                      isIndexBased={false}
                      label="State"
                      onChange={(selectedIndices) => {
                        // Convert indices to state names
                        const stateNames = statesWithAllOption.map((state) => state.name);
                        const selectedValues = selectedIndices.map((index) => stateNames[index]);

                        // Handle "All States" selection logic
                        if (
                          selectedValues.includes(ALL_STATES_OPTION) &&
                          selectedValues.length > 1
                        ) {
                          const specificStates = selectedValues.filter(
                            (state) => state !== ALL_STATES_OPTION
                          );
                          setField('states', specificStates);
                        } else {
                          setField(
                            'states',
                            selectedValues.length ? selectedValues : [ALL_STATES_OPTION]
                          );
                        }

                        // Reset counties when states change
                        setField(
                          'counties',
                          selectedValues.includes(ALL_STATES_OPTION) ? [ALL_COUNTIES_OPTION] : []
                        );
                      }}
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">States</div>
                      <div className="field-value selectorbg">
                        {!form?.states ||
                        form.states.length === 0 ||
                        form.states.includes(ALL_STATES_OPTION)
                          ? 'All States'
                          : form.states.join(', ')}
                      </div>
                    </div>
                  )}
                </Col>
                <Col lg={6} xs={12}>
                  {!readOnly ? (
                    <MultiSelectInput
                      key={`counties-${JSON.stringify(form.counties)}-${JSON.stringify(
                        form.states
                      )}`}
                      choices={getCountiesForStates(
                        form.states
                          .filter((state) => state !== ALL_STATES_OPTION)
                          .map((stateName) => states.find((s) => s.name === stateName)?.abbr)
                          .filter(Boolean)
                      )}
                      className="common-input"
                      controlId="createProgram.Counties"
                      defaultValue={form.counties.length ? form.counties : [ALL_COUNTIES_OPTION]}
                      disabled={
                        readOnly || !form.states.length || form.states.includes(ALL_STATES_OPTION)
                      }
                      isIndexBased={false}
                      label="County"
                      onChange={(selectedIndices) => {
                        const availableCounties = getCountiesForStates(
                          form.states
                            .filter((state) => state !== ALL_STATES_OPTION)
                            .map((stateName) => states.find((s) => s.name === stateName)?.abbr)
                            .filter(Boolean)
                        );

                        const selectedValues = selectedIndices.map(
                          (index) => availableCounties[index]
                        );

                        if (
                          selectedValues.includes(ALL_COUNTIES_OPTION) &&
                          selectedValues.length > 1
                        ) {
                          const specificCounties = selectedValues.filter(
                            (county) => county !== ALL_COUNTIES_OPTION
                          );
                          setField('counties', specificCounties);
                        } else {
                          setField(
                            'counties',
                            selectedValues.length ? selectedValues : [ALL_COUNTIES_OPTION]
                          );
                        }
                      }}
                    />
                  ) : (
                    <div className="field-heading-wraper">
                      <div className="field-heading">Counties</div>
                      <div className="field-value selectorbg">
                        {!form?.counties ||
                        form.counties.length === 0 ||
                        form.counties.includes(ALL_COUNTIES_OPTION)
                          ? 'All Counties'
                          : form.counties.join(', ')}
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  <MoneyInputNew
                    canVary
                    className="common-input"
                    controlId="createProgram.FundingAmount"
                    defaultAmount={form.fundingAmount}
                    defaultStringAmount={form.varyingFundingAmount}
                    defaultVaries={form.amountVaries ?? false}
                    errors={errors.fundingAmount}
                    hideRequiredIndicator
                    isFromPrograms={isFromPrograms}
                    label="Funding Amount"
                    onAmountChange={(newAmount) => setField('fundingAmount', newAmount)}
                    onVaryChange={(newVaries) => setField('amountVaries', newVaries)}
                    onVaryingAmountChange={(newAmount) =>
                      setField('varyingFundingAmount', newAmount)
                    }
                    required
                    text=""
                    textClass="currency-text"
                  />
                </Col>
                <Col lg={6} xs={12}>
                  <TextInput
                    className="common-input"
                    controlId="createProgram.MatchRequirements"
                    hideRequiredIndicator
                    label="Match Requirements"
                    onChange={(newValue) => setField('matchRequirements', newValue)}
                    placeholder="25% Match"
                    required
                  />
                </Col>
              </Row>
              <Row>
                <Col lg={6} xs={12}>
                  <TextInput
                    className="common-input"
                    controlId="createProgram.PeriodOfPerformance"
                    hideRequiredIndicator
                    label="Period of Performance"
                    onChange={(newValue) => setField('performancePeriod', newValue)}
                    placeholder="Enter Period of Performance"
                    required
                  />
                </Col>
                <Col lg={6} xs={12}>
                  <TextInput
                    className="common-input"
                    controlId="createProgram.EstimatedResponse"
                    hideRequiredIndicator
                    label="Estimated Response"
                    onChange={(newValue) => setField('estimatedResponse', newValue)}
                    placeholder="Enter Estimated Response"
                    required
                  />
                </Col>
              </Row>
              <Row>
                {currentUser.isMillenniumUser && (
                  <Col lg={6} xs={12}>
                    <div className="program-common-input">
                      <div className="d-flex flex-column mb-2">
                        <span className="show-for-clients-text small">Show for Flex Clients</span>
                        <Form.Check
                          checked={form.showForFlexClient ?? true}
                          className="show-flexclient-toggle"
                          id="showForFlexClient-toggle"
                          label=""
                          onChange={(e) => setField('showForFlexClient', e.target.checked)}
                          type="switch"
                        />
                      </div>
                    </div>
                  </Col>
                )}
              </Row>
            </Col>
          </Row>
        </div>
        {!readOnly && (
          <div className="mt-4 custom-fields-wrapper">
            <div
              className="hide-show-wrapper"
              onClick={toggleSectionVisibility}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  toggleSectionVisibility();
                }
              }}
              role="button"
              tabIndex={0}
            >
              <h5 className="heading-gb mb-4">Custom Fields</h5>
              <span className="ms-2" style={{ display: 'flex', alignItems: 'center' }}>
                {isSectionVisible ? (
                  <>
                    <KeyboardArrowUp
                      className="ms-2"
                      style={{ color: '#2C4474', fontSize: '18px' }}
                    />
                    <span className="show-hide-text">Hide Section</span>
                  </>
                ) : (
                  <>
                    <KeyboardArrowDown
                      className="ms-2"
                      style={{ color: '#2C4474', fontSize: '18px' }}
                    />{' '}
                    <span className="show-hide-text">Show Section</span>
                  </>
                )}
              </span>
            </div>
            {isSectionVisible && (
              <>
                <hr />
                <CustomFields onChange={(newFields) => setField('customFields', newFields)} />
              </>
            )}
          </div>
        )}
      </Form>
    </Container>
  );
}
