import React from 'react';
import { Container, Row, Col } from 'reactstrap';
import 'assets/scss/authwrapper.scss';
import headingImageSrc from 'assets/images/milliniumheading.png';

interface AuthWrapperProps {
  children: React.ReactNode;
  imageSrc: string;
}

export default function AuthWrapper({ children, imageSrc }: AuthWrapperProps) {
  return (
    <div className="auth-wrapper">
      <Container className="p-0 m-0" fluid>
        <Row className="align-items-center auth-container g-0 justify-content-center m-0 p-0">
          <Col className="auth-left d-flex flex-column m-0 p-0" md={6}>
            <img alt="Millennium Strategies" className="heading-image" src={headingImageSrc} />
            <div className="d-flex flex-grow-1 justify-content-center w-70 align-items-center">
              {children}
            </div>
            <a className="contact-link" href="/login" rel="noopener noreferrer" target="_blank">
              Contact Millennium
            </a>
            <p className="copyright">@2024 Millennium Strategies</p>
          </Col>
          <Col className="auth-right m-0 p-0" md={6}>
            <div className="auth-image h-100 w-100">
              <img alt="Auth" className="h-100 w-100" src={imageSrc} />
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
