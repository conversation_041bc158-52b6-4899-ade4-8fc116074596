import { Fragment } from 'react';
import Dropdown from 'react-bootstrap/Dropdown';

import { applicationsConfigs } from '../../../../constants/globalConstants';

interface StatusSelectorProps {
  status: string;
  disabled: boolean;
  onChange: (index: number) => void;
}

export default function StatusSelector({
  status = '0',
  disabled = false,
  onChange,
}: StatusSelectorProps) {
  return (
    <Dropdown>
      <Dropdown.Toggle className="form-control" disabled={disabled} variant={`status${status}`}>
        {applicationsConfigs.statuses.concat(applicationsConfigs.additionalCases)[+status]}
      </Dropdown.Toggle>
      <Dropdown.Menu style={{ padding: '0' }}>
        <Dropdown.Header className="text-center">Main Cases</Dropdown.Header>

        {/* TODO not secure option to pick index based on react index - expected performance issue // line 24 */}
        {applicationsConfigs.statuses
          .concat(applicationsConfigs.additionalCases)
          .map((item, index) => (
            <Fragment key={item}>
              {item === applicationsConfigs.additionalCases[0] && (
                <Dropdown.Header className="text-center">Other Cases</Dropdown.Header>
              )}
              <Dropdown.Item className={`btn-status${index}`} onClick={() => onChange(index)}>
                {item}
              </Dropdown.Item>
            </Fragment>
          ))}
      </Dropdown.Menu>
    </Dropdown>
  );
}
