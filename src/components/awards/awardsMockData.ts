import { format } from 'date-fns';
import { Award, awardStatuses } from 'types/awards';

const randomDate = (start: Date, end: Date) => {
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return format(date, 'yyyy-MM-dd');
};

const randomStatus = () => {
  return awardStatuses[Math.floor(Math.random() * awardStatuses.length)];
};

const randomNumber = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const awardsMockData: Award[] = [];
for (let i = 0; i < 25; i++) {
  awardsMockData.push({
    id: `#CNA-${3455 + i}`,
    assignee: `Assignee ${i}`,
    client: `Client ${i}`,
    department: i % 3 === 0 ? 'Planning' : 'Engineering',
    funder: `Funder ${i % 15}`,
    grantProgramName: `Program ${i % 15}`,
    startsOn: randomDate(new Date(2025, 0, 1), new Date(2025, 11, 31)),
    endsOn: randomDate(new Date(2026, 0, 1), new Date(2027, 11, 31)),
    status: randomStatus(),
    awardAmount: randomNumber(1000, 100000),
    awardExpended: randomNumber(1000, 100000),
    awardBalance: randomNumber(-10000, 100000),
    nextReportDue: randomDate(new Date(2025, 0, 1), new Date(2026, 11, 31)),
  });
}

export default awardsMockData;
