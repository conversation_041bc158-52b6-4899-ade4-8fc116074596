import { useFormikContext } from 'formik';
import SecondaryButton from 'shared/muiComponents/SecondaryButton';

function DiscardButton({ setUnsavedChanges }: { setUnsavedChanges?: (value: boolean) => void }) {
  const { resetForm } = useFormikContext();

  const handleDiscard = () => {
    if (setUnsavedChanges) setUnsavedChanges(false);

    resetForm();
  };

  return <SecondaryButton onClick={handleDiscard}>Discard</SecondaryButton>;
}

export default DiscardButton;
