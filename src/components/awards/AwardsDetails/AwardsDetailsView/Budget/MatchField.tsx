import { InputAdornment, TextField } from '@mui/material';
import { useState } from 'react';

import MatchModal from './MatchModal';
import { BudgetFormEntry } from '.';

export default function MatchField({
  value,
  onSave,
  data,
  awardMatchAmount,
  totalsMatchAmount,
  disabled = false,
  error = false,
}: {
  value: string;
  data: BudgetFormEntry;
  awardMatchAmount: number;
  totalsMatchAmount: number;
  onSave: (newValues: BudgetFormEntry) => void;
  disabled?: boolean;
  error?: boolean;
}) {
  const [isNotesOpen, setIsNotesOpen] = useState(false);
  const toggleOpen = () => setIsNotesOpen((prevState) => !prevState);

  return (
    <>
      <TextField
        disabled={disabled}
        error={error}
        fullWidth
        InputProps={{
          readOnly: true,
          endAdornment: <InputAdornment position="end">$</InputAdornment>,
        }}
        onClick={toggleOpen}
        size="small"
        sx={{
          '& .MuiInputBase-input': {
            textAlign: 'right',
            letterSpacing: '0.8px',
            cursor: 'pointer',
          },
          '& .Mui-disabled': {
            '& input': {
              WebkitTextFillColor: error ? 'red' : 'black',
              cursor: 'pointer',
            },
            '& fieldset': {
              borderColor: error ? 'red' : 'transparent',
            },
          },
          '& .MuiOutlinedInput-root': {
            '&.Mui-disabled': {
              '& fieldset': {
                border: error ? '1px solid red' : 'transparent',
              },
            },
            '& fieldset': {
              borderColor: error ? 'red' : 'transparent',
            },
            '& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
              display: 'none',
            },
            '& input[type=number]': {
              MozAppearance: 'textfield',
            },
            '&:hover fieldset': {
              borderColor: error ? 'red' : (theme) => theme.palette.primary.light,
            },
            '&.Mui-focused fieldset': {
              borderColor: error ? 'red' : (theme) => theme.palette.primary.main,
            },
          },
        }}
        value={value}
      />

      {isNotesOpen && (
        <MatchModal
          awardMatchAmount={awardMatchAmount}
          data={data}
          onSave={onSave}
          toggle={toggleOpen}
          totalsMatchAmount={totalsMatchAmount}
        />
      )}
    </>
  );
}
