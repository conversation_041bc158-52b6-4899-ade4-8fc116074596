/* eslint-disable no-else-return */
import { isIndirectTotal } from './BudgetTable';
import { BudgetFormEntry } from './index';

const localeCompareName = (a: BudgetFormEntry, b: BudgetFormEntry): number => {
  if (a.name === null && b.name === null) {
    return 0;
  }

  if (a.name === null) {
    return 1;
  }

  if (b.name === null) {
    return -1;
  }
  return a.name.localeCompare(b.name, undefined, { sensitivity: 'base' });
};

type BudgetField =
  | 'awardAmount'
  | 'awardExpended'
  | 'awardBalance'
  | 'matchAmount'
  | 'matchExpended'
  | 'matchBalance';

export type Totals = {
  [key in BudgetField]: number;
};

// Utility: names of special total rows
const TOTAL_ROW_NAMES = ['Total Direct Charges', 'Indirect Charges'];

// Custom sort: itemOrder, then name, totals always last
function budgetEntrySort(a: BudgetFormEntry, b: BudgetFormEntry): number {
  const aIsTotal = TOTAL_ROW_NAMES.includes(a.name);
  const bIsTotal = TOTAL_ROW_NAMES.includes(b.name);

  if (aIsTotal && !bIsTotal) return 1;
  if (!aIsTotal && bIsTotal) return -1;
  if (aIsTotal && bIsTotal)
    return TOTAL_ROW_NAMES.indexOf(a.name) - TOTAL_ROW_NAMES.indexOf(b.name);

  if (a.itemOrder != null && b.itemOrder != null) return a.itemOrder - b.itemOrder;
  if (a.itemOrder != null) return -1;
  if (b.itemOrder != null) return 1;
  return localeCompareName(a, b);
}

export default (
  items: BudgetFormEntry[],
  isVersionData: boolean
): { sortedItems: BudgetFormEntry[]; totals: Totals } => {
  const groups = new Map<number | null, BudgetFormEntry[]>();

  items = items.map((item) => ({
    ...item,
    matchExpended:
      typeof item.matchExpended === 'string' ? parseFloat(item.matchExpended) : item.matchExpended,
    matchBalance:
      typeof item.matchBalance === 'string' ? parseFloat(item.matchBalance) : item.matchBalance,
    awardExpended:
      typeof item.awardExpended === 'string' ? parseFloat(item.awardExpended) : item.awardExpended,
    awardBalance:
      typeof item.awardBalance === 'string' ? parseFloat(item.awardBalance) : item.awardBalance,
    awardAmount:
      typeof item.awardAmount === 'string' ? parseFloat(item.awardAmount) : item.awardAmount,
    matchAmount:
      typeof item.matchAmount === 'string' ? parseFloat(item.matchAmount) : item.matchAmount,
  }));

  items = items.filter((item) => item.enabled !== false);

  // Build parent-child relationships
  items.forEach((item) => {
    const parentKey = isVersionData ? item.parentId : item.parentId;
    if (!groups.has(parentKey)) {
      groups.set(parentKey, []);
    }
  });
  // Populate the groups with items
  items.forEach((item) => {
    const parentKey = isVersionData ? item.parentId : item.parentId;
    groups.get(parentKey)!.push(item);
  });

  // Sort each group by itemOrder, then name, totals last
  groups.forEach((group) => {
    group.sort(budgetEntrySort);
  });

  // Helper: find leaf nodes (no children)
  const getLeafNodes = (allItems: BudgetFormEntry[]): BudgetFormEntry[] => {
    const parentIdSet = new Set(
      allItems
        .filter((item) => item.parentId !== null && item.parentId !== undefined)
        .map((item) => String(item.parentId))
    );
    return allItems.filter(
      (item) =>
        !parentIdSet.has(String(isVersionData ? item.entryId : item.id)) &&
        !TOTAL_ROW_NAMES.includes(item.name)
    );
  };

  // Always calculate balances for each item
  items = items.map((item) => ({
    ...item,
    awardBalance: (item.awardAmount || 0) - (item.awardExpended || 0),
    matchBalance: (item.matchAmount || 0) - (item.matchExpended || 0),
  }));

  // Only sum leaf nodes for totals
  const leafNodes = getLeafNodes(items);
  const totals: Totals = {
    awardAmount: leafNodes.reduce((acc, item) => acc + (item.awardAmount || 0), 0),
    awardExpended: leafNodes.reduce((acc, item) => acc + (item.awardExpended || 0), 0),
    awardBalance: leafNodes.reduce(
      (acc, item) => acc + ((item.awardAmount || 0) - (item.awardExpended || 0)),
      0
    ),
    matchAmount: leafNodes.reduce((acc, item) => acc + (item.matchAmount || 0), 0),
    matchExpended: leafNodes.reduce((acc, item) => acc + (item.matchExpended || 0), 0),
    matchBalance: leafNodes.reduce(
      (acc, item) => acc + ((item.matchAmount || 0) - (item.matchExpended || 0)),
      0
    ),
  };

  // Recursively build sorted items, calculating parent values from children
  const sortedItems: BudgetFormEntry[] = [];
  const appendItems = (parentId: number | null) => {
    if (groups.has(parentId)) {
      const group = groups.get(parentId);
      group?.forEach((item) => {
        const children = groups.get(isVersionData ? Number(item.entryId) : Number(item.id)) || [];
        const hasChildren = children.length > 0;
        let calculated = { ...item };
        if (hasChildren) {
          // Calculate parent values from children
          const childrenAmounts = children.reduce(
            (sums, child) => ({
              awardAmount: sums.awardAmount + (child.awardAmount || 0),
              awardExpended: sums.awardExpended + (child.awardExpended || 0),
              awardBalance:
                sums.awardBalance + ((child.awardAmount || 0) - (child.awardExpended || 0)),
              matchAmount: sums.matchAmount + (child.matchAmount || 0),
              matchExpended: sums.matchExpended + (child.matchExpended || 0),
              matchBalance:
                sums.matchBalance + ((child.matchAmount || 0) - (child.matchExpended || 0)),
            }),
            {
              awardAmount: 0,
              awardExpended: 0,
              awardBalance: 0,
              matchAmount: 0,
              matchExpended: 0,
              matchBalance: 0,
            }
          );
          calculated = {
            ...item,
            hasChildren,
            awardAmount: childrenAmounts.awardAmount,
            awardExpended: childrenAmounts.awardExpended,
            awardBalance: childrenAmounts.awardBalance,
            matchAmount: childrenAmounts.matchAmount,
            matchExpended: childrenAmounts.matchExpended,
            matchBalance: childrenAmounts.matchBalance,
            hasError: childrenAmounts.awardBalance < 0 || childrenAmounts.matchBalance < 0,
            editable: false, // parent rows not editable
          };
        } else {
          calculated = {
            ...item,
            hasChildren,
            hasError: item.awardBalance < 0 || item.matchBalance < 0,
            editable: true, // leaf nodes editable
          };
        }
        // Make 'Total Direct Charges' always calculated and read-only
        if (isIndirectTotal(item)) {
          calculated = {
            ...calculated,
            awardAmount: totals.awardAmount,
            awardExpended: totals.awardExpended,
            awardBalance: totals.awardBalance,
            matchAmount: totals.matchAmount,
            matchExpended: totals.matchExpended,
            matchBalance: totals.matchBalance,
            editable: false,
          };
        }
        sortedItems.push(calculated);
        appendItems(isVersionData ? Number(item.entryId) : Number(item.id));
      });
    }
  };
  appendItems(null);
  return { sortedItems, totals };
};
