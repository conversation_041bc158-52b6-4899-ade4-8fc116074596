// TODO: Remove - Not used anymore

import { FormikProps } from 'formik';
import { Box, Grid, Typography } from '@mui/material';

import { Award } from 'types/awards';
import CurrencyInput from 'shared/muiComponents/CurrencyInputField';
import useUserSession from 'hooks/useUserSession';

// import { awardConfigs } from '../../../../../constants/globalConstants';

// const parseValue = (value: string | number) => value.toString().replace(/[^0-9.]/g, '');

export default function FinancialDetails({
  awardEditForm,
  setUnsavedChanges,
}: {
  awardEditForm: FormikProps<Partial<Award>>;
  setUnsavedChanges: (unsavedChanges: boolean) => void;
}) {
  const {
    values: {
      awardAmount,
      matchAmount,
      awardExpended,
      awardBalance,
      matchExpended,
      matchBalance,
      // status,
    },
    setFieldValue,
    handleBlur,
    errors,
  } = awardEditForm;

  const { userType } = useUserSession();

  const canEditAward = userType !== 'millenniumAnalyst' && userType !== 'millenniumResearcher';
  // status !== awardConfigs.statuses.closed &&

  return (
    <Box my={4}>
      <Typography color="primary.main" mb={2} variant="h2">
        Financial Details
      </Typography>

      <Grid container spacing={2}>
        <Grid item md={6} xs={12}>
          <CurrencyInput
            errorText={errors.awardAmount}
            id="awardAmount"
            label="Award Amount"
            onBlur={handleBlur}
            onSave={(value: number) => setFieldValue('awardAmount', value)}
            readonly={!canEditAward}
            required
            value={awardAmount}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <CurrencyInput
            id="matchAmount"
            label="Match"
            onBlur={handleBlur}
            onSave={(value: number) => setFieldValue('matchAmount', value)}
            readonly={!canEditAward}
            value={matchAmount}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <CurrencyInput
            id="awardBalance"
            label="Award Balance"
            onBlur={handleBlur}
            // onSave={(value: number) => setFieldValue('awardBalance', value)}
            readonly
            value={awardBalance}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <CurrencyInput
            id="awardExpended"
            label="Award Expended"
            onBlur={handleBlur}
            // onSave={(value: number) => setFieldValue('awardExpended', value)}
            readonly
            value={awardExpended}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <CurrencyInput
            id="matchBalance"
            label="Match Balance"
            onBlur={handleBlur}
            // onSave={(value: number) => setFieldValue('matchBalance', value)}
            readonly
            value={matchBalance}
          />
        </Grid>
        <Grid item md={6} xs={12}>
          <CurrencyInput
            id="matchExpended"
            label="Match Expended"
            onBlur={handleBlur}
            // onSave={(value: number) => setFieldValue('matchExpended', value)}
            readonly
            value={matchExpended}
          />
        </Grid>
      </Grid>
    </Box>
  );
}
