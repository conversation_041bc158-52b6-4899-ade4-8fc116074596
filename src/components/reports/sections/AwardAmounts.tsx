import { useContext, useState } from 'react';
import BootstrapTable from 'react-bootstrap-table-next';
import { formatCurrencyValue } from 'utils/utilFunctions';
import { StringsContext } from 'index';
import { KeyboardArrowUp, KeyboardArrowDown } from '@mui/icons-material';
import { IAwardAmountsData } from 'pages/Reports';
import 'assets/scss/Reports.scss';

function AwardAmounts({ data }: { data: IAwardAmountsData[] }) {
  const {
    applications: { sourceNames, categoryNames },
  } = useContext(StringsContext);

  const [showSection, setShowSection] = useState(true);

  function categoryFormatter(cell: number) {
    return categoryNames[cell] ?? cell;
  }

  function awardAmountFormatter(cell: number) {
    return formatCurrencyValue(cell);
  }

  // The sourceNames array is already initialized when the page is loaded.
  const columns = [
    {
      dataField: 'category',
      text: 'Category',
      formatter: categoryFormatter,
      classes: 'custom-first-column',
    },
    ...sourceNames.map((sourceName, sourceIndex) => {
      return {
        dataField: `source${sourceIndex}`,
        text: sourceName,
        formatter: awardAmountFormatter,
      };
    }),
  ];

  return (
    <div className="w-100 section-wrapper">
      <div
        className="hide-show-wrapper"
        onClick={() => setShowSection(!showSection)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            setShowSection(!showSection);
          }
        }}
        role="button"
        tabIndex={0}
      >
        <h5 className="section-heading">
          Total Award Amounts by Category and Source
          <span className="ms-2" style={{ display: 'flex', alignItems: 'center' }}>
            {showSection ? (
              <>
                <KeyboardArrowUp className="ms-2" style={{ color: '#2C4474', fontSize: '18px' }} />
                <span className="show-hide-text">Hide Section</span>
              </>
            ) : (
              <>
                <KeyboardArrowDown
                  className="ms-2"
                  style={{ color: '#2C4474', fontSize: '18px' }}
                />{' '}
                <span className="show-hide-text">Show Section</span>
              </>
            )}
          </span>
        </h5>
      </div>
      {showSection && (
        <div style={{ marginTop: '32px' }}>
          <BootstrapTable
            bootstrap4
            columns={columns}
            data={data}
            keyField="id"
            remote={{
              filter: true,
            }}
            striped
            wrapperClasses="table-responsive table-borderless"
          />
        </div>
      )}
    </div>
  );
}

export default AwardAmounts;
