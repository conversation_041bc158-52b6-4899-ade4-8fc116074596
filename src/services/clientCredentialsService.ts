import { AxiosResponse } from 'axios';
import { ClientCredentials } from 'types/client';

import api, { outputError } from './apiService';

export const getClientCredentials = (id: string): Promise<ClientCredentials> =>
  api
    .get(`/clients?page=1&perPage=10000&id=${id}&target=credential`)
    .then((response: AxiosResponse) => {
      if (response.status < 302) return response.data;
      return {} as ClientCredentials;
    })
    .catch((error) => {
      outputError(error);
      return {} as ClientCredentials;
    });

export const createClientCredentials = (data: ClientCredentials): Promise<boolean> =>
  api
    .post(`/clients`, { ...data, target: 'credential' })
    .then((response: AxiosResponse) => response.status < 302)
    .catch((error) => {
      outputError(error);
      return false;
    });

export const updateClientCredentials = (data: ClientCredentials): Promise<boolean> =>
  api
    .put(`/clients`, { ...data, target: 'credential' })
    .then((response: AxiosResponse) => response.status < 302)
    .catch((error) => {
      outputError(error);
      return false;
    });

export const deleteClientCredentials = (id: string): Promise<boolean> =>
  api
    .delete(`/clients`, { data: { id, target: 'credential' } })
    .then((response: AxiosResponse) => response.status < 302)
    .catch((error) => {
      outputError(error);
      return false;
    });

export const checkClientCredentials = (password: string): Promise<boolean> =>
  api
    .post(`/clients/masterpass`, { password })
    .then((response: AxiosResponse) => response.status < 302)
    .catch((error) => {
      outputError(error);
      return false;
    });
