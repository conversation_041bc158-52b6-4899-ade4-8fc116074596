import { AxiosResponse } from 'axios';
import { saveAs } from 'file-saver';

import { User } from 'types/user';
import { Program } from 'types/program';
import api, { outputError } from './apiService';

const { REACT_APP_PROGRAMS_PER_PAGE, REACT_APP_API_URL } = process.env;

export interface ProgramList {
  rows: Program[];
  pages: number;
  count: number;
  error?: string;
}

export interface ReadProgram {
  programId: number;
  users: string;
}

export const createProgram = (data: Partial<Program> & { user?: User }): Promise<Program> =>
  api
    .post(`/programs`, data)
    .then((response: AxiosResponse) => {
      if (response.status < 303) {
        return response?.data;
      }
      throw new Error(`Failed to create program: ${response.statusText}`);
    })
    .catch((error) => {
      outputError(error);
      throw error;
    });

export const getPrograms = (filterQuery: string): Promise<ProgramList> =>
  api
    .get(`/programs?${filterQuery}`)
    .then((response: AxiosResponse) => {
      if (response.status < 303)
        return {
          rows: response.data.rows,
          pages: Math.ceil(response.data.count / Number(REACT_APP_PROGRAMS_PER_PAGE || '20')),
          count: response.data.count,
        };
      return { rows: [], pages: 0, count: 0, error: response.statusText || 'Error' };
    })
    .catch((error) => {
      outputError(error);
      return { rows: [], pages: 0, count: 0, error: 'Error' };
    });

export const searchPrograms = (
  searchQuery: string,
  searchField: string,
  funder: string | null
): Promise<Program[]> =>
  api
    .get(`/programs/search?query=${searchQuery}&field=${searchField}&funder=${funder}`)
    .then((response: AxiosResponse) => {
      if (response.status < 303) return response.data;
      return [];
    })
    .catch((error) => {
      outputError(error);
      return [];
    });

export const updateProgram = (data: Partial<Program> & { user?: User }): Promise<boolean> =>
  api
    .put(`/programs`, data)
    .then((response: AxiosResponse) => response.status < 303)
    .catch((error) => {
      outputError(error);
      return false;
    });

export const deleteProgram = (id: number): Promise<boolean> =>
  api
    .delete('/programs', { data: { id } })
    .then((response: AxiosResponse) => response.status < 303)
    .catch((error) => {
      outputError(error);
      return false;
    });

export const getProgramsReadList = (userId: string): Promise<ReadProgram[]> =>
  api
    .get(`/programs/read/${userId}`)
    .then((response: AxiosResponse) => {
      if (response.status < 303) return response.data;
      return [];
    })
    .catch((error) => {
      outputError(error);
      return [];
    });

export const markProgramsAsRead = (data: unknown): Promise<boolean> =>
  api
    .post('/programs/read', data)
    .then((response: AxiosResponse) => response.status < 303)
    .catch((error) => {
      outputError(error);
      return false;
    });

export const downloadProgramFile = (path: string) =>
  api
    .get(`${REACT_APP_API_URL}/file/local/summaries/${path}`, { responseType: 'blob' })
    .then((response: AxiosResponse) => {
      saveAs(new Blob([response.data]), `${path}`);
    })
    .catch((error) => {
      outputError(error);
    });

export const deleteprogramFile = (id: number): Promise<boolean> =>
  api
    .delete('programs/deletefile', { id })
    .then((response: AxiosResponse) => response.status < 303)
    .catch((error) => {
      outputError(error);
      return false;
    });

export const getProgram = (id: number): Promise<Program | null> => {
  console.log('programService: getProgram called with ID:', id);
  return api
    .get(`/programs/${id}`)
    .then((response: AxiosResponse) => {
      console.log(`programService: getProgram received response for ID ${id}:`, response);
      if (response.status < 303) {
        // Normalize the data to ensure consistent format
        const programData = response.data;

        console.log(`programService: Raw program data from API for ID ${id}:`, programData);

        // Create normalized program object with default values for missing fields
        const normalizedProgram = {
          ...programData,
          funder: programData.funder || '',
          fundingAmount: programData.fundingAmount || 0,
          amountVaries: programData.amountVaries || false,
          varyingFundingAmount: programData.varyingFundingAmount || '',
          source: Array.isArray(programData.source)
            ? programData.source
            : programData.source !== undefined && programData.source !== null
              ? [programData.source]
              : [],
          category: Array.isArray(programData.category)
            ? programData.category
            : programData.category !== undefined && programData.category !== null
              ? [programData.category]
              : [],
          states: Array.isArray(programData.states)
            ? programData.states
            : programData.states
              ? [programData.states]
              : [],
          counties: Array.isArray(programData.counties)
            ? programData.counties
            : programData.counties
              ? [programData.counties]
              : [],
          orgTypes: Array.isArray(programData.orgTypes)
            ? programData.orgTypes
            : programData.orgTypes
              ? [programData.orgTypes]
              : [],
          showForFlexClient: programData.showForFlexClient ?? true,
        };

        console.log(`programService: Normalized program data for ID ${id}:`, normalizedProgram);
        return normalizedProgram;
      }
      console.error(
        `programService: Failed to fetch program with ID ${id}: ${response.statusText}`
      );
      throw new Error(`Failed to fetch program: ${response.statusText}`);
    })
    .catch((error) => {
      console.error(`programService: Error fetching program with ID ${id}:`, error);
      outputError(error);
      return null; // Return null instead of throwing
    });
};
