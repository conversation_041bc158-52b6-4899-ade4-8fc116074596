{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "strictPropertyInitialization": false,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "./src",
    "paths": {
      "*": ["*"],
      "assets/*": ["assets/*"],
      "components/*": ["components/*"],
      "constants/*": ["constants/*"],
      "hooks/*": ["hooks/*"],
      "pages/*": ["pages/*"],
      "routes/*": ["routes/*"],
      "services/*": ["services/*"],
      "shared/*": ["shared/*"],
      "types/*": ["types/*"],
      "utils/*": ["utils/*"]
    },
    "incremental": true, // Added for incremental compilation
    "sourceMap": false, // Disabled source map generation
    "include": ["src/**/*.tsx", "src/custom.d.ts"],
    "exclude": ["node_modules"]
  }
}
